syntax = "proto3";
package recv_sys;


message Empty
{
}

message OrderEntryRequest
{
    string symbol = 1;
    string ord_type = 2;
    int32 ord_px = 3;
    int32 direction = 4;
    int32 ord_qty = 5;
    string exec_trade_id = 6;
    string strat_name = 7;
    string slave_name = 8;
}

message Ack
{
    int32 status = 1;
}

message IdUpdateRequest
{
    int32 new_kivi_ord_num = 1;
    string new_exec_ord_num = 2;
}

message CurrExecSymbol
{
    string symbol = 1;
}

message ModifRequest
{
    int32 exec_ord_num = 1;
    int32 ord_px = 2;
    int32 ord_qty = 3;
}

message ExecOrderNumList
{
    repeated int32 exec_ord_num_list = 1;
}

service RecvSignalHandler
{
    rpc sendOrder(OrderEntryRequest) returns (Ack) {}
    rpc updateIds(IdUpdateRequest) returns (Ack) {}
    rpc clearCurrExecSymbol(CurrExecSymbol) returns (Ack) {}
    rpc manualCancellation(ExecOrderNumList) returns (Ack) {}
    rpc manualStartModification(ModifRequest) returns (Ack) {}
    rpc manualStopModification(ExecOrderNumList) returns (Ack) {}
}
