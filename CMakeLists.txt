cmake_minimum_required(VERSION 3.15.0)
project(Aurora VERSION 0.1.0)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -pthread")

include(ExternalProject)
ExternalProject_Add(
  spdlog
  PREFIX spdlog
  GIT_REPOSITORY https://github.com/gabime/spdlog.git
  GIT_TAG v1.8.5
  SOURCE_DIR "${CMAKE_CURRENT_BINARY_DIR}/spdlog-src"
  BINARY_DIR "${CMAKE_CURRENT_BINARY_DIR}/spdlog-build"
  CMAKE_ARGS -DCMAKE_BUILD_TYPE=${CMAKE_BUILD_TYPE}
			 -DCMAKE_CXX_COMPILER=${CMAKE_CXX_COMPILER}
			 -DCMAKE_INSTALL_PREFIX=${PROJECT_SOURCE_DIR}
			 -DSPDLOG_BUILD_SHARED=OFF)

include(CMakeLists.txt.grpc)
include_directories(${CMAKE_SOURCE_DIR}/src ${CMAKE_SOURCE_DIR}/include)

find_package(CppKafka REQUIRED)
find_package(Protobuf REQUIRED)

set(PROTO_FILE protos/TradingLog.proto)
protobuf_generate_cpp(PROTO_SRCS PROTO_HDRS ${PROTO_FILE})

set(SOURCES ./src/main.cpp ./src/CReceiver.cpp ./src/COrderManager.cpp
      ./src/CSender.cpp ./src/CUtility.cpp ./src/CDataManager.cpp
      ./src/CLogger.cpp ./src/CServerForCTCL.cpp)

add_executable(main ${SOURCES} ${PROTO_SRCS} ${PROTO_HDRS})
add_dependencies(main spdlog)

find_path(HIREDIS_HEADER hiredis)
target_include_directories(main PUBLIC ${HIREDIS_HEADER})

find_library(HIREDIS_LIB hiredis)
target_link_libraries(main ${HIREDIS_LIB})

find_path(REDIS_PLUS_PLUS_HEADER sw)
target_include_directories(main PUBLIC ${REDIS_PLUS_PLUS_HEADER})

find_library(REDIS_PLUS_PLUS_LIB redis++)
target_link_libraries(main ${REDIS_PLUS_PLUS_LIB})

find_library(PQXX_LIB pqxx)
find_library(PQ_LIB pq)
target_link_libraries(main ${PQXX_LIB} ${PQ_LIB})

target_link_libraries(main hw_grpc_proto ${_REFLECTION} ${_GRPC_GRPCPP}
					  ${_PROTOBUF_LIBPROTOBUF})

target_link_libraries(main CppKafka::cppkafka ${PROTOBUF_LIBRARIES})
target_compile_options(main PRIVATE -Wno-pmf-conversions)