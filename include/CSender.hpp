/**
 * @file CSender.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2021-08-03
 * 
 * @copyright Copyright (c) 2021
 * 
 */
#ifndef CSENDER_HPP
#define CSENDER_HPP

#include "sky.grpc.pb.h"
#include <grpcpp/ext/proto_server_reflection_plugin.h>
#include <grpcpp/grpcpp.h>
#include <grpcpp/health_check_service_interface.h>
#include "structures.h"
#include "CUtility.hpp"
#include "CReader.cpp"
#include "COrderManager.hpp"

using grpc::Channel;
using grpc::ClientContext;
using grpc::Server;
using grpc::ServerBuilder;

using aurora::SignalHandler;

class COrderManager;

class CSender final : public SignalHandler::Service
{
	int exchangeClosingMinute;
	COrderManager *ord_mngr; // = nullptr;
	std::unique_ptr<Server> server;
	std::unique_ptr<SignalHandler::Stub> stub_;

	std::mutex send_queue_mtx;
	std::queue<OrderInfo> send_queue;

	void sendOrder(const OrderInfo &order);
	void sendModification(const OrderInfo &order);
	void sendCancellation(const OrderInfo &order);
	// Pop packets from send queue and based on params, send Entry,
	// modification or cancellation requests
	void sendRequests();
	void startThreads();

	OrderInfo popOrderInfo();

public:
	CSender(std::shared_ptr<Channel> channel);
	void setOrdManager(COrderManager *_ord_mngr);
	void pushOrderInfo(OrderInfo order);
	// Get Ord and Trd info packets and send to COrderManager::processResponses
	void getLatestOrderInfo();
	void getLatestTradeInfo();
	int updateKiviOrdNum(std::int32_t new_kivi_ord_num);
	void getOrdStatus(std::string exec_ord_num);
	bool getCTCLLoginStatus();
	void sendLogOffRequest();
};

#endif
