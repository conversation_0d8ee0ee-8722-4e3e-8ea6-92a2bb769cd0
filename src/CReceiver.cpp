/**
 * @file CReceiver.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2021-08-03
 *
 * @copyright Copyright (c) 2021
 *
 */
#include "CReceiver.hpp"

CReceiver::<PERSON><PERSON><PERSON>ver(COrderManager *_ord_mngr, CSender *_sender)
{
	this->ord_mngr = _ord_mngr;
	this->sender = _sender;
	logger->sys_info("Receiver initialized");
}

void CReceiver::updateMarketSnapForLogging(const std::string symbol)
{
	std::string data = data_rdr->readKey(symbol);
	market_snap_log.clear();
	CUtility::split(data, '|', market_snap_log);

	try{
		int str_len;

		// Removing Decimal in prices to convert prices in rupees to paise
		// removing decimal from qty and vol
		for(int index = 6; index < market_snap_log.size(); index += 3){
			str_len = market_snap_log[index].size();
			market_snap_log[index] = market_snap_log[index].substr(0,str_len-3);

			str_len = market_snap_log[index+1].size();
			market_snap_log[index+1] = market_snap_log[index+1].substr(0,str_len-3) + market_snap_log[index+1].substr(str_len-2);

			str_len = market_snap_log[index+2].size();
			market_snap_log[index+2] = market_snap_log[index+2].substr(0,str_len-3);
	}
	}
	catch (std::exception &ex)
	{
		logger->raise_error(fmt::format("market_snap_log update error : {}", ex.what()));
	}


	market_snap_log.resize(41, "");
	if (market_snap_log[0] != "")
	{
		market_snap_log[0] = market_snap_log[0].substr(11, 8) + ".000";
	}
	return;
}

std::string CReceiver::getTotalBuyQtyForLog()
{
	int qty = 0;
	for (int i = 0; i < 5; i++)
	{
		if (this->market_snap_log[6 + i * 3] != "")
		{
			qty += std::stoi(this->market_snap_log[6 + i * 3]);
		}
	}
	return std::to_string(qty);
}

std::string CReceiver::getTotalSellQtyForLog()
{
	int qty = 0;
	for (int i = 0; i < 5; i++)
	{
		if (this->market_snap_log[21 + i * 3] != "")
		{
			qty += std::stoi(this->market_snap_log[21 + i * 3]);
		}
	}
	return std::to_string(qty);
}

grpc::Status CReceiver::sendOrder(ServerContext *context,
								const recv_sys::OrderEntryRequest *req,
								recv_sys::Ack *ack)
{
	std::int32_t curr_px = req->ord_px();
	std::int32_t buy_flag = req->direction();
	std::string symbol = req->symbol();
	std::string ord_type = req->ord_type();
	std::int32_t ord_qty = req->ord_qty();
	int additional_price_tick = 1;
	if (symbol.find("CRUDEOIL") != std::string::npos) {
		additional_price_tick = 2;
	}

	logger->ord_info(fmt::format(
		"Received entry signal from BaLTE : ({}, {}, {}, {} lots, {} strategy, {} slave)",
		req->exec_trade_id(), req->symbol(), req->direction(), req->ord_qty(), req->strat_name(), req->slave_name()));
	// * ord_type = "XX" for roll overs
	if (ord_type == "NORMAL" || ord_type == "XX")
		ord_type = "LMT";
	if (curr_px > 0)
	{
		logger->ord_error("CReceiver : BaLTE order px > 0");
		curr_px = 0;
	}

	if (ord_qty < 0)
	{
		logger->ord_error("CReceiver : BaLTE ord qty < 0");
		ord_qty *= -1;
	}

	if (curr_px <= 0)
	{
		curr_px = data_rdr->readSymbol(symbol, (buy_flag == 1) ? "bid1" : "ask1");
		if (curr_px == -1)
		{
			logger->ord_error(fmt::format(
				"CReceiver : Current price couldn't be fetched for symbol {}",
				symbol));
			return grpc::Status(grpc::StatusCode::NOT_FOUND,
								"Symbol data cannot be fetched");
		}
		else if (price_tick.contains(symbol))
		{
			curr_px += ((buy_flag == 1) ? 1 : -1) * additional_price_tick * price_tick[symbol];

			// corner case - ask
			if(curr_px <= 0){
			curr_px = price_tick[symbol];
			}

			curr_px = ((curr_px + price_tick[symbol]/2) / price_tick[symbol])*price_tick[symbol];
		}
	}
	if (curr_px == 0)
	{
		logger->ord_error(fmt::format(
			"CReceiver : Best bid is 0 for symbol {}. Specify order price.",
			symbol));
		return grpc::Status(grpc::StatusCode::NOT_FOUND,
							"Symbol data cannot be fetched");
	}

	Contract contract = CUtility::splitContractName(symbol);
	std::string curr_day = CUtility::getCurrDay("%Y-%m-%d");
	std::string segment = "FUTCOM";
	std::uint32_t curr_ts = CUtility::convertTSToEpoch(curr_day, "%Y-%m-%d");
	std::uint32_t exp_ts =
		CUtility::convertTSToEpoch(contract.expiry, "%d-%b-%Y");
	if (contract.strike != "0"){
		segment = "OPTCOM";
	}			
	if (curr_ts > exp_ts)
	{
		logger->raise_error(fmt::format(
			"[Teams Alert] CReceiver : Order ({}, {}, {} lots, {}). "
			"Current date {} is after expiry date {} buffer of 1 day.",
			symbol, buy_flag, ord_qty, req->exec_trade_id(), curr_day,
			contract.expiry));
	}
	else
	{
		this->updateMarketSnapForLogging(symbol);
		logger->kafka_log(
			fmt::format("{8},{0},{1},{2},{3},{4},{5},{6},0,0,0,0,,,,,{7},,0",
						contract.symbol, contract.expiry, contract.option_type,
						contract.strike, ord_qty, (buy_flag == 1) ? "B" : "S",
						req->exec_trade_id(), req->strat_name(), segment));
		
		// Send log in proto format
		google::protobuf::Timestamp order_book_timestamp = CUtility::TimeStringToTimestamp(this->market_snap_log[0]);
		ExecutionLogs::BaseLog new_order_base_log = CUtility::getBaseLogObject();
		ExecutionLogs::NewOrderLog* new_order_log = new_order_base_log.mutable_new_order_log();
		new_order_log->set_instrument(segment);
		new_order_log->set_symbol(contract.symbol);
		new_order_log->set_expiry_date(contract.expiry);
		new_order_log->set_option_type(contract.option_type);
		new_order_log->set_strike(std::stod(contract.strike));
		new_order_log->set_trade_qty(ord_qty);
		new_order_log->set_buy_sell((buy_flag == 1) ? ExecutionLogs::BuySell::buy : ExecutionLogs::BuySell::sell);
		new_order_log->set_trade_id(std::stoll(req->exec_trade_id()));
		new_order_log->set_strategy_name(req->strat_name());
		new_order_log->set_slave_name(req->slave_name());
		
		// Order book
		ExecutionLogs::OrderBookLog* order_book = new_order_log->mutable_order_book();
		order_book->set_buy_price_1(std::stoi(this->market_snap_log[7]));
		order_book->set_sell_price_1(std::stoi(this->market_snap_log[22]));
		order_book->set_last_traded_price(std::stoi(this->market_snap_log[3]));
		google::protobuf::Timestamp* ltt = order_book->mutable_last_traded_time();
		ltt->CopyFrom(order_book_timestamp);
		order_book->set_total_buy_qty(std::stoll(this->getTotalBuyQtyForLog()));
		order_book->set_total_sell_qty(std::stoll(this->getTotalSellQtyForLog()));
		order_book->set_avg_traded_price(0);
		google::protobuf::Timestamp* exch_send_time = order_book->mutable_exch_send_time();
		exch_send_time->CopyFrom(order_book_timestamp);
		google::protobuf::Timestamp* system_receive_time = order_book->mutable_system_receive_time();
		system_receive_time->CopyFrom(order_book_timestamp);

		logger->kafka_send_proto(new_order_base_log);

		OrderRequest order(symbol, ord_type, buy_flag, curr_px, ord_qty,
						req->exec_trade_id());
		order.direction = (std::int16_t)(buy_flag);

		logger->sys_info(
			fmt::format("CReceiver : Received order request {}, {}, {}, {}", symbol,
						buy_flag, ord_qty, curr_px));
		this->ord_mngr->pushOrderRequest(order);
	}
	ack->set_status(0);
	return grpc::Status::OK;
}

grpc::Status CReceiver::updateIds(ServerContext *context,
								const recv_sys::IdUpdateRequest *req,
								recv_sys::Ack *ack)
{
	int exec_update_status = 0, kivi_update_status = 0;
	if (!(req->new_exec_ord_num() == ""))
	{
		int new_exec_ord_num = std::stoi(req->new_exec_ord_num());
		exec_update_status = this->ord_mngr->setExecOrdNum(new_exec_ord_num);
		if (exec_update_status == 0)
		{
			logger->sys_info(
				fmt::format("CReceiver : Set exec_ord_num to {}", new_exec_ord_num));
		}
	}
	kivi_update_status = this->sender->updateKiviOrdNum(req->new_kivi_ord_num());
	ack->set_status(exec_update_status + kivi_update_status);
	return grpc::Status::OK;
}

grpc::Status CReceiver::clearCurrExecSymbol(ServerContext *context,
											const recv_sys::CurrExecSymbol *req,
											recv_sys::Ack *ack)
{
	std::string symbol = req->symbol();
	this->ord_mngr->clearCurrExecSymbol(symbol);
	ack->set_status(0);
	return grpc::Status::OK;
}

grpc::Status
CReceiver::manualCancellation(ServerContext *context,
							const recv_sys::ExecOrderNumList *req,
							recv_sys::Ack *ack)
{
	for (auto exec_ord_num : req->exec_ord_num_list())
		this->ord_mngr->manualCancellation(exec_ord_num);
	return grpc::Status::OK;
}

grpc::Status
CReceiver::manualStartModification(ServerContext *context,
								const recv_sys::ModifRequest *req,
								recv_sys::Ack *ack)
{
	this->ord_mngr->manualStartModification(req->exec_ord_num(), req->ord_px(),
											req->ord_qty());
	return grpc::Status::OK;
}

grpc::Status
CReceiver::manualStopModification(ServerContext *context,
								const recv_sys::ExecOrderNumList *req,
								recv_sys::Ack *ack)
{
	for (auto exec_ord_num : req->exec_ord_num_list())
		this->ord_mngr->manualStopModification(exec_ord_num);
	return grpc::Status::OK;
}

void CReceiver::serve(void *v)
{
	std::string grpc_addr = CUtility::getConfig("gRPC", "receiver_grpc_ip", "") +
							":" +
							CUtility::getConfig("gRPC", "receiver_grpc_port", "");
	grpc::EnableDefaultHealthCheckService(true);
	grpc::reflection::InitProtoReflectionServerBuilderPlugin();
	ServerBuilder builder;
	builder.AddListeningPort(grpc_addr, grpc::InsecureServerCredentials());
	builder.RegisterService(this);
	server = builder.BuildAndStart();
	logger->sys_info(fmt::format(
		"CReceiver : Receiver gRPC server listening on {}", grpc_addr));
	server->Wait();
}

void CReceiver::stopServer()
{
	if (server)
	{
		server->Shutdown();
		logger->sys_info("CReceiver : Shutting down Receiver gRPC server");
	}
	else
	{
		logger->sys_info("CReceiver : Receiver gRPC server not running");
	}
}
