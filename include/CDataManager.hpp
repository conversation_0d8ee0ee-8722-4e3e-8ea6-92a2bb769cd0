/**
 * @file CDataManager.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2021-08-20
 *
 * @copyright Copyright (c) 2021
 *
 */

#ifndef CDATAMANAGER_HPP
#define CDATAMANAGER_HPP
#include "CUtility.hpp"
#include <mutex>
#include <pqxx/pqxx>
#include <spdlog/fmt/fmt.h>

/**
 * @brief Orderbook table schema
 *
 */
#define ORD_SCHEMA                                                             \
	"symbol VARCHAR(40), ord_type VARCHAR(3), direction INT, ord_px INT, "     \
	"trig_px INT, ord_qty INT, disc_qty INT,"                                  \
	"balte_trd_num NUMERIC, exec_ord_num VARCHAR(10), ord_conf_ts TIMESTAMP, " \
	"is_new BOOLEAN, is_modif "                                                \
	"BOOLEAN, is_cancel BOOLEAN, is_reject BOOLEAN, trade_flag BOOLEAN,"       \
	"entry_day TIMESTAMP, send_modif_ts TIMESTAMP, trd_qty INT, rej_reason "   \
	"VARCHAR(100)"

/**
 * @brief Orderbook columns
 *
 */
#define ORD_COLS                                                   \
	"symbol, ord_type, direction, ord_px, "                        \
	"trig_px, ord_qty, disc_qty,"                                  \
	"balte_trd_num, exec_ord_num, ord_conf_ts, is_new, is_modif, " \
	"is_cancel, is_reject, trade_flag, "                           \
	"entry_day, send_modif_ts, trd_qty, rej_reason"

/**
 * @brief Tradebook table schema
 *
 */
#define TRD_SCHEMA                                                            \
	"symbol VARCHAR(40), buy_flag INT, qty_traded INT, trd_px INT, trd_ts "   \
	"TIMESTAMP, entry_day TIMESTAMP, exec_ord_num VARCHAR(10), exch_trd_num " \
	"NUMERIC, balte_trd_num NUMERIC"

/**
 * @brief Tradebook columns
 *
 */
#define TRD_COLS                                     \
	"symbol, buy_flag, qty_traded, trd_px, trd_ts, " \
	"entry_day , exec_ord_num, exch_trd_num, balte_trd_num"

#define BALTE_SCHEMA                                                           \
	"entry_day TIMESTAMP, symbol VARCHAR(40), ord_type VARCHAR(3), direction " \
	"INT, ord_px INT, "                                                        \
	"ord_qty INT, total_ord_qty INT, balte_trd_num NUMERIC"

#define BALTE_COLS                                                             \
	"entry_day, symbol, ord_type, direction, ord_px, ord_qty, total_ord_qty, " \
	"balte_trd_num"

#define META_DATA_SCHEMA                                         \
	"entry_day date UNIQUE NOT NULL, exec_ord_counter INTEGER, " \
	"exch_ord_counter INTEGER"
#define META_DATA_COLS "entry_day, exec_ord_counter, exch_ord_counter"
#define RMS_PARAMS_SCHEMA "daily_exposure_limit NUMERIC"
#define RMS_PARAMS_COLS "daily_exposure_limit"

#define ORD_PRIMARY_KEY "entry_day, exec_ord_num"
#define TRD_PRIMARY_KEY "entry_day, exec_ord_num, exch_trd_num"
#define BALTE_PRIMARY_KEY "entry_day, balte_trd_num"
#define RMS_PARAMS_PRIMARY_KEY "daily_exposure_limit"
#define DEFAULT_EXEC_ORD_COUNTER 100000
#define DEFAULT_EXCH_ORD_COUNTER 100

/**
 * @brief Class to perform CRUD operations in postgres database.
 *
 */
class CDataManager
{
	std::mutex db_mtx;
	pqxx::connection *conn;
	std::string db_name, db_ord_table, db_trd_table, db_rms_params_table,
		db_meta_table, db_balte_table;

public:
	/**
	 * @brief Construct a new CDataManager object
	 *
	 */
	CDataManager();

	/**
	 * @brief Destroy the CDataManager object
	 *
	 */
	~CDataManager() { conn->close(); }

	/**
	 * @brief Create a table in the database
	 *
	 * @param table_name std::string table name
	 * @param type std::string
	 */
	void createTable(std::string table_name, std::string type);

	void insertOrderInfo(const OrderInfo &pack);
	void insertOrderInfo(const OrderRequest &pack);
	void updateOrderInfo(const OrderInfo &pack);
	void updateOrderInfo(const OrderRequest &pack);
	void deleteOrderInfo(const OrderInfo &pack);
	void insertTradeInfo(const OrderInfo &pack);
	int getOrderCounterNumber(bool is_exec = true);
	int updateOrderCounter(int curr_counter, bool is_exec = true);
	std::vector<std::vector<std::string>> getPendingOrdersFromDB();
	std::vector<std::vector<std::string>> getBaLTEOrdersFromDB();
};
#endif