cmake_minimum_required(VERSION 3.15.0)

set(CMAKE_PREFIX_PATH /home/<USER>/resources/mcx-execution-system/)
include(common.cmake)

get_filename_component(sender_proto "./protos/sky.proto" ABSOLUTE)
get_filename_component(sender_proto_path "${sender_proto}" PATH)
get_filename_component(receiver_proto "./protos/receiver.proto" ABSOLUTE)
get_filename_component(receiver_proto_path "${receiver_proto}" PATH)

set(sender_proto_srcs "${CMAKE_CURRENT_BINARY_DIR}/sky.pb.cc")
set(sender_proto_hdrs "${CMAKE_CURRENT_BINARY_DIR}/sky.pb.h")
set(sender_grpc_srcs "${CMAKE_CURRENT_BINARY_DIR}/sky.grpc.pb.cc")
set(sender_grpc_hdrs "${CMAKE_CURRENT_BINARY_DIR}/sky.grpc.pb.h")

set(receiver_proto_srcs "${CMAKE_CURRENT_BINARY_DIR}/receiver.pb.cc")
set(receiver_proto_hdrs "${CMAKE_CURRENT_BINARY_DIR}/receiver.pb.h")
set(receiver_grpc_srcs "${CMAKE_CURRENT_BINARY_DIR}/receiver.grpc.pb.cc")
set(receiver_grpc_hdrs "${CMAKE_CURRENT_BINARY_DIR}/receiver.grpc.pb.h")

add_custom_command(
	OUTPUT "${sender_proto_srcs}" "${sender_proto_hdrs}" "${sender_grpc_srcs}"
				 "${sender_grpc_hdrs}"
	COMMAND
		${_PROTOBUF_PROTOC} ARGS --grpc_out "${CMAKE_CURRENT_BINARY_DIR}" --cpp_out
		"${CMAKE_CURRENT_BINARY_DIR}" -I "${sender_proto_path}"
		--plugin=protoc-gen-grpc="${_GRPC_CPP_PLUGIN_EXECUTABLE}" "${sender_proto}"
	DEPENDS "${sender_proto}")

add_custom_command(
	OUTPUT "${receiver_proto_srcs}" "${receiver_proto_hdrs}"
				 "${receiver_grpc_srcs}" "${receiver_grpc_hdrs}"
	COMMAND
		${_PROTOBUF_PROTOC} ARGS --grpc_out "${CMAKE_CURRENT_BINARY_DIR}" --cpp_out
		"${CMAKE_CURRENT_BINARY_DIR}" -I "${receiver_proto_path}"
		--plugin=protoc-gen-grpc="${_GRPC_CPP_PLUGIN_EXECUTABLE}"
		"${receiver_proto}"
	DEPENDS "${receiver_proto}")

include_directories("${CMAKE_CURRENT_BINARY_DIR}")

add_library(
	hw_grpc_proto
	${sender_grpc_srcs}
	${sender_grpc_hdrs}
	${sender_proto_srcs}
	${sender_proto_hdrs}
	${receiver_grpc_srcs}
	${receiver_grpc_hdrs}
	${receiver_proto_srcs}
	${receiver_proto_hdrs})
target_link_libraries(hw_grpc_proto ${_REFLECTION} ${_GRPC_GRPCPP}
											${_PROTOBUF_LIBPROTOBUF})