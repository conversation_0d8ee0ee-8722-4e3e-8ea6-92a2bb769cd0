/**
 * @file CUtility.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2021-08-20
 *
 * @copyright Copyright (c) 2021
 *
 */
#ifndef CUTILITY_CPP
#define CUTILITY_CPP
#include "CUtility.hpp"

/**
 * @brief Get the local epoch time
 *
 * @return time_t
 */
time_t CUtility::getEpochTime() { return std::time(nullptr); }

/**
 * @brief Returns the current time as a `tm` structure
 *
 * @return struct tm*
 */
struct tm CUtility::getCurrTimeStamp()
{
	time_t rawtime;
	struct tm timeinfo;

	time(&rawtime);
	localtime_r(&rawtime, &timeinfo);
	return timeinfo;
}

/**
 * @brief Returns current time as a std::string in the specified format
 *
 * @param s_format Defaults to `%Y-%m-%d %H:%M:%S`
 * @return std::string
 */
std::string CUtility::getCurrTimeStampStr(std::string s_format)
{
	char buffer[80];
	std::string currTS;
	struct tm timeinfo = getCurrTimeStamp();
	strftime(buffer, 80, s_format.c_str(), &timeinfo);
	currTS.assign(buffer, 19);
	return currTS;
}

/**
 * @brief Returns current day in the specified format.
 *
 * @return std::string Defaults to "%Y-%m-%d"
 */
std::string CUtility::getCurrDay(std::string s_format)
{
	return getCurrTimeStampStr(s_format + " %H:%M:%S").substr(0, 10);
}

/**
 * @brief Converts epoch time into timestamp as a string of given format.
 *
 * @param epoch epoch time in seconds
 * @param s_format timestamp format
 * @return std::string
 */
std::string CUtility::convertEpochToTS(uint32_t epoch, std::string s_format)
{
	time_t rawtime = epoch;
	struct tm *timeinfo;
	timeinfo = gmtime(&rawtime);
	const int MAX_LEN = 80;
	char c_expiry_date[MAX_LEN];
	strftime(c_expiry_date, MAX_LEN, s_format.c_str(), timeinfo);
	std::string s_ts(c_expiry_date);
	return s_ts;
}

/**
 * @brief Convert a string timestamp to epoch seconds
 *
 * If date parse fails, 0 is returned
 *
 * @param s_ts std::string timestamp
 * @param s_format timestamp format
 * @return std::uint32_t
 */
std::uint32_t CUtility::convertTSToEpoch(std::string s_ts,
										 std::string s_format)
{
	struct tm ts = {};
	auto ptr = strptime(s_ts.c_str(), s_format.c_str(), &ts);
	if (ptr && !(*ptr))
		return std::mktime(&ts);
	else
		return 0;
}

/**
 * @brief Get value of a variable from Config.ini
 *
 * @param section
 * @param var_name
 * @param default_val
 * @return std::string
 */
std::string CUtility::getConfig(std::string section, std::string var_name,
								std::string default_val)
{
	CSimpleIni ini;
	ini.SetUnicode();
	SI_Error rc = ini.LoadFile(CONFIG_FILE_PATH);
	if (rc < 0)
	{
		logger->sys_error("Can't load config file");
		return "";
	};
	const char *var;
	var = ini.GetValue(section.c_str(), var_name.c_str(), default_val.c_str());
	std::string s_var(var);
	return s_var;
}

/**
 * @brief Write Section, Key and Value to the Config.ini file
 *
 * @param section Section
 * @param var_name Key
 * @param value Value
 */
void CUtility::setConfig(std::string section, std::string var_name,
						 std::string value)
{
	CSimpleIni ini;
	ini.SetUnicode();
	SI_Error rc = ini.LoadFile(CONFIG_FILE_PATH);
	rc = ini.SetValue(section.c_str(), var_name.c_str(), value.c_str());
	if (rc < 0)
	{
		logger->sys_error("Can't set value in config file");
	}
	rc = ini.SaveFile(CONFIG_FILE_PATH);
	if (rc < 0)
	{
		logger->sys_error("Couldn't save config file");
	};
}

/**
 * @brief Remove trailing whitespaces from string
 *
 * @param word
 * @return std::string
 */
std::string CUtility::rstrip(std::string word)
{
	int i = word.length();
	while (--i >= 0)
	{
		if ((word[i] != '\n') && (word[i] != '\r') && (word[i] != '\t') &&
			(word[i] != ' '))
			break;
	}
	return word.substr(0, i + 1);
}

/**
 * @brief Split a string by `delim` and append tokens to `words`
 *
 * @param line std::string
 * @param delim delimiter
 * @param words std::vector<std::string> passed by reference where words will be
 * stored
 */
void CUtility::split(std::string const &line, const char delim,
					 std::vector<std::string> &words)
{
	std::stringstream stream(line);
	std::string word;
	while (std::getline(stream, word, delim))
	{
		words.push_back(rstrip(word));
	}
}

/**
 * @brief Populate the value_coeff map by parsing MCXScrips
 * Value_coeff is lot_size* (price_num/price_denum)* (general_num/general_denum)
 * The value of 1 lot of any symbol at price px: px*value_coeff[symbol]
 * @return void
 */
void CUtility::populateValueCoeff()
{
	std::string scrip_path = CUtility::getConfig("PreMarket", "scrip_path", "");
	std::ifstream infile(scrip_path);
	std::string line, symbol, expiry_date, option_type, strike;
	std::vector<std::string> words;
	while (std::getline(infile, line))
	{
		words.clear();
		if (line.length() == 0)
			continue;
		CUtility::split(line, ',', words);
		expiry_date = "";
		option_type = words[56];
		if (option_type == "XX")
			option_type = "";
		strike = (words[55] != "0") ? words[55] : "";

		if (((words[54]).length() > 0) &&
			((words[53] == "FUTIDX") | (words[53] == "FUTCOM") | (words[53] == "OPTCOM") |
			 (words[53] == "OPTFUT")))
		{
			if (words[54] != "0")
				expiry_date =
					CUtility::convertEpochToTS(std::stoi(words[54]), "%d-%b-%Y");
		}
		symbol = words[6] + expiry_date + option_type + strike;
		value_coeff[symbol] = std::stof(words[20]) *
							  (std::stof(words[83]) / std::stof(words[84])) *
							  (std::stof(words[80]) / std::stof(words[82]));
		price_tick[symbol] = std::stoi(words[21]);
	}
}

/**
 * @brief Update Exchange closing time as per Day light savings
   Parsing mcx_daylight_savings to check whether current day is there or not
   If not there then exchange closing time updated to 23:55 instead of 23:30
 * @return void
 */
int CUtility::updateExchangeClosing()
{
	std::string mcx_daylight_savings = CUtility::getConfig("PreMarket", "mcx_daylight_savings", "");
	std::ifstream infile(mcx_daylight_savings);
	std::string line, dateToCheck;
	std::vector<std::string> words;
	std::string currDay = getCurrDay("%Y-%m-%d");
	while (std::getline(infile, line))
	{
		words.clear();
		if (line.length() == 0)
			continue;
		CUtility::split(line, ',', words);
		dateToCheck = words[0];
		if(currDay == dateToCheck){
			return 30;
		}
	}
	return 55;
}

int CUtility::getRandomDelay()
{
	std::random_device rd;
	std::mt19937 mt(rd());
	std::uniform_real_distribution<double> dist(modif_delay_lw_lmt,
												modif_delay_up_lmt);
	return dist(mt);
}

Contract CUtility::splitContractName(std::string contract_name)
{
	size_t index_1 = std::string::npos, index_2 = std::string::npos;
	index_1 = contract_name.find('-', 0);
	if (index_1 != std::string::npos)
		if (contract_name.length() > (index_1 + 1))
			index_2 = contract_name.find('-', index_1 + 1);
	Contract contract;
	if (index_1 == std::string::npos)
		return contract;
	else
	{
		contract.symbol = contract_name.substr(0, index_1 - 2);
		contract.expiry = contract_name.substr(index_1 - 2, 11);
		if (index_2 != std::string::npos)
		{
			if (contract_name.length() > (index_2 + 4 + 1))
			{
				contract.option_type = contract_name.substr(index_2 + 4 + 1, 2);
				contract.strike = contract_name.substr(index_2 + 4 + 1 + 2);
			}
		}
		if (contract.option_type == "")
		{
			contract.option_type = "XX";
		}
		if (contract.strike == "")
		{
			contract.strike = "0";
		}
	}
	return contract;
}

ExecutionLogs::BaseLog CUtility::getBaseLogObject()
{
	ExecutionLogs::BaseLog base_log;

    google::protobuf::Timestamp* timestamp = base_log.mutable_timestamp();
    google::protobuf::Timestamp current_time = google::protobuf::util::TimeUtil::GetCurrentTime();

    // Create a TimeZone object for IST
    // Add the IST offset to the current time
    const int64_t kIstOffsetSeconds = 19800;  // 5 hours and 30 minutes in seconds
    current_time.set_seconds(current_time.seconds() + kIstOffsetSeconds);
    timestamp->CopyFrom(current_time);
	return base_log;
}

google::protobuf::Timestamp CUtility::TimeStringToTimestamp(const std::string& time_str) {
	auto now = std::chrono::system_clock::now();
    std::time_t now_c = std::chrono::system_clock::to_time_t(now);
    std::tm now_tm = *std::localtime(&now_c);
	
	// Set time portion from input string
    std::tm time_tm{};
    std::istringstream ss(time_str);
    ss >> std::get_time(&time_tm, "%T");

	now_tm.tm_hour = time_tm.tm_hour;
    now_tm.tm_min = time_tm.tm_min;
    now_tm.tm_sec = time_tm.tm_sec;

	char buffer[80];
    std::strftime(buffer, 80, "%Y-%m-%dT%H:%M:%SZ", &now_tm);
	google::protobuf::Timestamp timestamp;
    google::protobuf::util::TimeUtil::FromString(buffer, &timestamp);
	return timestamp;
}
#endif
