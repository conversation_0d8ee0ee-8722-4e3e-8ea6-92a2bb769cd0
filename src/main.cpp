/**
 * @file main.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2021-08-03
 *
 * @copyright Copyright (c) 2021
 *
 */

#include "CReceiver.hpp"
#include "COrderManager.hpp"
#include "CSender.hpp"
#include "CServerForCTCL.hpp"

int main(int argc, char *argv[])
{
	logger = new CLogger();
	logger->sys_info("Execution system initialized");
	ExecutionLogs::BaseLog status_base_log = CUtility::getBaseLogObject();
	ExecutionLogs::SystemStatus* status_log = status_base_log.mutable_system_status();
	status_log->set_status("Started");
	logger->kafka_send_proto(status_base_log);
	modif_delay_lw_lmt = std::stoi(CUtility::getConfig("Limits", "modif_delay_lw_lmt", "60"));
	modif_delay_up_lmt = std::stoi(CUtility::getConfig("Limits", "modif_delay_up_lmt", "180"));

	CUtility::populateValueCoeff();

	data_mngr = new CDataManager();
	data_rdr = new CReader();

	std::string grpc_addr = CUtility::getConfig("gRPC", "sender_grpc_ip", "") +
							":" +
							CUtility::getConfig("gRPC", "sender_grpc_port", "");

	CSender sender(
		grpc::CreateChannel(grpc_addr, grpc::InsecureChannelCredentials()));
	COrderManager ord_mngr(&sender);
	sender.setOrdManager(&ord_mngr);
	CReceiver recv(&ord_mngr, &sender);
	std::future<void> __recv__ =
		std::async(std::launch::async, (THREADFUNCPTR)&CReceiver::serve, &recv);

	CServerForCTCL server_ctcl;
	std::future<void> __ctcl_server__ =
		std::async(std::launch::async, (THREADFUNCPTR)&CServerForCTCL::serve, &server_ctcl);
	server_ctcl.setOrdManager(&ord_mngr);

	struct tm curr_ts = CUtility::getCurrTimeStamp();
	while (true)
	{
		if ((curr_ts.tm_hour >= 23) && (curr_ts.tm_min >= 59))
		{
			sender.sendLogOffRequest();
			break;
		}
		curr_ts = CUtility::getCurrTimeStamp();
		sleep(30);
	}
	recv.stopServer();
	return 0;
}