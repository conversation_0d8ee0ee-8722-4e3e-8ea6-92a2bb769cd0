version: "3.5"

volumes: 
    exec_data_lake:

services:
    postgres:
        image: postgres:latest
        container_name: exec_db
        volumes: 
            - exec_data_lake:/var/lib/postgresql/data/
        ports: 
            - 5434:5432
        environment:
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: postgres
            PGDATA: /var/lib/postgresql/data/container_data

    exec_sys:
        build:
            context: .
            dockerfile: Dockerfile
            network : host
        image: exec_sys
        stdin_open: true
        tty: true
        volumes: 
            - exec_data_lake:/data/
        depends_on:
            - postgres
        network_mode: host
