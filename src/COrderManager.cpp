/**
 * @file COrderManager.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief Manage and track all orders and trades
 * @version 0.0.1
 * @date 2021-06-25
 *
 * @copyright Copyright (c) 2021
 *
 */

#include "COrderManager.hpp"
COrderManager::COrder<PERSON>anager(CSender *_sender)
{
	this->sender = _sender;
	this->ord_counter = data_mngr->getOrderCounterNumber();
	if (this->ord_counter == DEFAULT_EXEC_ORD_COUNTER)
		data_mngr->updateOrderCounter(this->ord_counter);
	if (this->exch_ord_counter == DEFAULT_EXCH_ORD_COUNTER)
		data_mngr->updateOrderCounter(this->exch_ord_counter, false);
	this->exch_ord_counter = data_mngr->getOrderCounterNumber(false);
	this->repopulatePendingOrders();
	logger->ord_info(fmt::format("COrderManager : Set the order counter to : {}",
								 this->ord_counter));
	__process_entries__ =
		std::async(std::launch::async,
				   (THREADFUNCPTR)&COrderManager::processEntryRequests, this);
	__process_pending__ =
		std::async(std::launch::async,
				   (THREADFUNCPTR)&COrderManager::processPendingRequests, this);
}

void COrderManager::pushOrderRequest(OrderRequest order)
{
	std::int32_t oqs = 0;
	logger->lock_info(fmt::format("pushOrderRequest : Acquiring ord_queue lock for TradeID: {}", order.balte_id));
	this->order_queue_mtx.lock();
	this->order_queue.push(order);
	oqs = order_queue.size();
	this->order_queue_mtx.unlock();
	logger->lock_info(fmt::format("pushOrderRequest : Released ord_queue lock for Trade ID: {}", order.balte_id));
	logger->ord_info(fmt::format(
		"pushOrderRequest : Pushed Order with TradeID: {} and ({}, {}, {}, {} lots) to order_queue having current size: {}",
		order.balte_id, order.symbol, order.direction, order.ord_px, order.ord_qty, oqs));
}

OrderRequest COrderManager::popOrderRequest()
{
	logger->lock_info("popOrderRequest : Acquiring ord_queue lock");
	this->order_queue_mtx.lock();
	OrderRequest order = this->order_queue.front();

	std::int32_t oqs = 0;
	logger->lock_info(fmt::format("popOrderRequest : Acquired ord_queue lock for TradeID: {}", order.balte_id));
	this->order_queue.pop();
	oqs = order_queue.size();
	this->order_queue_mtx.unlock();
	logger->lock_info(fmt::format("popOrderRequest : Released ord_queue lock for TradeID: {}", order.balte_id));
	logger->ord_info(fmt::format(
		"popOrderRequest : Popped Order with TradeID: {} and ({}, {}, {}, {} lots) from order_queue having current size: {}",
		order.balte_id, order.symbol, order.direction, order.ord_px, order.ord_qty, oqs));
	return order;
}

/**
 * @brief Decide the optimal order size based on the current market snapshot
 * Logic will vary based on the liquidity of the symbol and the order price
 * should not exceed the maximum transaction price limit set by the exchange
 *
 * @param symbol
 * @param direction
 * @return std::uint32_t
 */
std::uint32_t COrderManager::getCurrOrderSize(std::string symbol,
											  std::uint32_t direction)
{
	int min_ord_quantity =
		std::stoi(CUtility::getConfig("SplitSize", "min_order_quantity", "2"));
	int max_single_transaction_value = std::stoi(CUtility::getConfig(
		"SplitSize", "max_single_transaction_value", "12000000"));
	int order_size, best_quantity;
	try
	{
		// Get the LTP for the current Symbol
		int ltp = data_rdr->readSymbol(symbol, "ltp");

		// Use LTP & max single transaction value to find max single transaction
		// quantity
		int max_single_transaction_qty = std::floor(
			max_single_transaction_value * 100 / abs(ltp * value_coeff[symbol]));

		// Get best bid/ask quantity
		best_quantity = std::abs(data_rdr->readSymbol(
			symbol, (direction == 1) ? "ask1_vol" : "bid1_vol"));

		order_size = std::min(max_single_transaction_qty, 2*best_quantity);
	}
	catch (const std::exception &ex)
	{
		logger->sys_error(fmt::format(
			"GetCurrentOrderSize failed with : {0}, sending min ord quantity: {1}",
			ex.what(), min_ord_quantity));
		order_size = min_ord_quantity;
	}
	// Limiting Order size to 6 -> so that at max 12 lot order can be sent
	//if(order_size > 6){
		order_size = 10;
	//}
	return std::max(min_ord_quantity, order_size);
}

/**
 * @brief Perform netoff of the split order present in orders_to_send_map
 * and the current order to be placed. Subsequently, generate two Trd
 * responses if netoff occurs
 *
 * @param split_order Order present in he orders_to_send_map
 * @param curr_order Order to be added to orders_to_send_map
 */
void COrderManager::netoffOrders(OrderRequest &split_order,
								 OrderRequest &curr_order)
{
	// * No netoff operation if direction of both the orders is same
	if (split_order.direction == curr_order.direction)
		return;

	// * Netoff quantity is the minimum of two orders in opposite direction
	std::uint32_t netoff_qty = std::min(split_order.ord_qty, curr_order.ord_qty);
	if (netoff_qty > 0)
	{
		logger->ord_info(fmt::format("netOffOrders : Netting off order with existing TradeID: {} and current TradeID: {}", split_order.balte_id, curr_order.balte_id));
		split_order.ord_qty -= netoff_qty;
		curr_order.ord_qty -= netoff_qty;
		std::uint32_t curr_ts = CUtility::getEpochTime();

		logger->sys_info(fmt::format("netoffOrders : calling updateMarketSnapForLogging for existing TradeID: {} and current TradeID: {}", split_order.balte_id, curr_order.balte_id));
		this->updateMarketSnapForLogging(split_order.symbol);
		logger->sys_info(fmt::format("netoffOrders : completed updateMarketSnapForLogging for existing TradeID: {} and current TradeID: {}", split_order.balte_id, curr_order.balte_id));

		std::int32_t netoff_px = std::stoi(this->market_snap_log[7]);

		// * Create a new order packet for the split order which was net-ed-off
		OrderInfo split_order_info(split_order.symbol, split_order.ord_type,
								   split_order.direction, split_order.ord_px, 0, 0,
								   0, this->ord_counter, curr_ts, true, false,
								   false, false, "", 0, true, netoff_px, netoff_qty,
								   curr_ts, split_order.balte_id);
		this->ord_counter = data_mngr->updateOrderCounter(this->ord_counter);
		// * Add the netoff order to ord_hist
		logger->lock_info(fmt::format("netOffOrders : Acquiring ord_hist lock for netting off existing TradeID: {}", split_order.balte_id));
		this->ord_hist_mtx.lock();
		this->ord_hist[split_order_info.exec_ord_num] = split_order_info;
		this->ord_hist_mtx.unlock();
		logger->lock_info(fmt::format("netOffOrders : Released ord_hist lock for netting off existing TradeID: {}", split_order.balte_id));

		// * Create a new order packet for the current order which was net-ed-off
		OrderInfo curr_order_info(curr_order.symbol, curr_order.ord_type,
								  curr_order.direction, curr_order.ord_px, 0, 0, 0,
								  this->ord_counter, curr_ts, true, false, false,
								  false, "", 0, true, netoff_px, netoff_qty,
								  curr_ts, curr_order.balte_id);
		this->ord_counter = data_mngr->updateOrderCounter(this->ord_counter);
		// * Add the netoff order to ord_hist
		logger->lock_info(fmt::format("netOffOrders : Acquiring ord_hist lock for netting off current TradeID: {}", curr_order.balte_id));
		this->ord_hist_mtx.lock();
		this->ord_hist[curr_order_info.exec_ord_num] = curr_order_info;
		this->ord_hist_mtx.unlock();
		logger->lock_info(fmt::format("netOffOrders : Released ord_hist lock for netting off current TradeID: {}", curr_order.balte_id));

		this->exch_ord_counter =
			data_mngr->updateOrderCounter(this->exch_ord_counter, false);
		// * Create an OrderPacket for the split order
		aurora::OrderPacket split_order_packet;
		split_order_packet.set_exec_ord_num(
			std::to_string(split_order_info.exec_ord_num));
		split_order_packet.set_kivi_ord_num(NETOFF_KIVI_ID);
		split_order_packet.set_exch_ord_num(this->exch_ord_counter);
		split_order_packet.set_symbol(split_order_info.symbol);
		split_order_packet.set_order_price(split_order_info.ord_px);
		split_order_packet.set_direction(split_order_info.direction);
		split_order_packet.set_ord_qty(split_order_info.ord_qty);
		split_order_packet.set_order_ts(split_order_info.ord_conf_ts);
		split_order_packet.set_order_type(split_order_info.ord_type);
		split_order_packet.set_status(ENT_CONF);
		split_order_packet.set_rej_reason("");
		split_order_packet.set_trd_qty(split_order_info.trd_qty);
		data_mngr->insertOrderInfo(split_order_info);
		processOrdResponses(split_order_packet);

		// * Create the Trd responses for the netoff order
		aurora::TradePacket split_trade_packet;
		split_trade_packet.set_exec_ord_num(
			std::to_string(split_order_info.exec_ord_num));
		split_trade_packet.set_kivi_ord_num(NETOFF_KIVI_ID);
		split_trade_packet.set_exch_ord_num(this->exch_ord_counter);
		split_trade_packet.set_trd_num(this->exch_ord_counter);
		split_trade_packet.set_symbol(split_order_info.symbol);
		split_trade_packet.set_trade_price(netoff_px);
		split_trade_packet.set_direction(split_order_info.direction);
		split_trade_packet.set_quantity(netoff_qty);
		split_trade_packet.set_trade_ts(curr_ts);
		processTrdResponses(split_trade_packet);

		this->exch_ord_counter =
			data_mngr->updateOrderCounter(this->exch_ord_counter, false);
		// * Create an OrderPacket for the netoff order
		aurora::OrderPacket curr_order_packet;
		curr_order_packet.set_exec_ord_num(
			std::to_string(curr_order_info.exec_ord_num));
		curr_order_packet.set_kivi_ord_num(NETOFF_KIVI_ID);
		curr_order_packet.set_exch_ord_num(this->exch_ord_counter);
		curr_order_packet.set_symbol(curr_order_info.symbol);
		curr_order_packet.set_order_price(curr_order_info.ord_px);
		curr_order_packet.set_direction(curr_order_info.direction);
		curr_order_packet.set_ord_qty(curr_order_info.ord_qty);
		curr_order_packet.set_order_ts(curr_order_info.ord_conf_ts);
		curr_order_packet.set_order_type(curr_order_info.ord_type);
		curr_order_packet.set_status(ENT_CONF);
		curr_order_packet.set_rej_reason("");
		curr_order_packet.set_trd_qty(curr_order_info.trd_qty);
		data_mngr->insertOrderInfo(curr_order_info);
		processOrdResponses(curr_order_packet);

		aurora::TradePacket curr_trade_packet;
		curr_trade_packet.set_exec_ord_num(
			std::to_string(curr_order_info.exec_ord_num));
		curr_trade_packet.set_kivi_ord_num(NETOFF_KIVI_ID);
		curr_trade_packet.set_exch_ord_num(this->exch_ord_counter);
		curr_trade_packet.set_trd_num(this->exch_ord_counter);
		curr_trade_packet.set_symbol(curr_order_info.symbol);
		curr_trade_packet.set_trade_price(netoff_px);
		curr_trade_packet.set_direction(curr_order_info.direction);
		curr_trade_packet.set_quantity(netoff_qty);
		curr_trade_packet.set_trade_ts(curr_ts);
		processTrdResponses(curr_trade_packet);
	}
}

/**
 * @brief Pick the symbol, go to the list of split orders for the
 * curr_symbol present in orders_to_send_map and netoff the split
 * orders
 *
 * @param curr_order Current order which has to be net-ed-off
 * @return OrderRequest
 */
OrderRequest COrderManager::netoffSymbol(OrderRequest curr_order)
{
	logger->lock_info(fmt::format("netOffSymbol : Acquiring orders_to_send_map lock for TradeID: {}", curr_order.balte_id));
	this->orders_to_send_map_mtx.lock();
	if (orders_to_send_map.contains(curr_order.symbol))
	{
		auto list_itr = orders_to_send_map[curr_order.symbol].begin();
		auto list_end = orders_to_send_map[curr_order.symbol].end();

		while (list_itr != list_end)
		{
			netoffOrders(*list_itr, curr_order);
			if (list_itr->ord_qty == 0)
				// * Remove the order from the list
				list_itr = orders_to_send_map[curr_order.symbol].erase(list_itr);
			else
				list_itr++;

			if (curr_order.ord_qty == 0)
				break;
		}
	}
	this->orders_to_send_map_mtx.unlock();
	logger->lock_info(fmt::format("netOffSymbol : Released orders_to_send_map lock for TradeID: {}", curr_order.balte_id));
	return curr_order;
}

/**
 * @brief Split the order into multiple chunks according to logic defined
 * in getCurrOrderSize and add to orders_to_send_map
 *
 * @param curr_order
 */
void COrderManager::addOrderToSendMap(OrderRequest curr_order)
{
	logger->lock_info(fmt::format("addOrderToSendMap : Acquiring lock on orders_to_send_map for TradeID: {}", curr_order.balte_id));
	this->orders_to_send_map_mtx.lock();
	if (curr_order.ord_qty > 0)
	{
		if (!(this->orders_to_send_map.contains(curr_order.symbol)))
			this->orders_to_send_map[curr_order.symbol] = std::list<OrderRequest>();
		this->orders_to_send_map[curr_order.symbol].push_back(curr_order);
		logger->ord_info(
			fmt::format("addOrderToSendMap : Pushed order with TradeID: {} to orders_to_send_map",
						curr_order.balte_id));
	}
	this->orders_to_send_map_mtx.unlock();
	logger->lock_info(fmt::format("addOrderToSendMap : Released lock on orders_to_send_map for TradeID: {}", curr_order.balte_id));

	data_mngr->insertOrderInfo(curr_order);
}

OrderInfo COrderManager::getOrderFromSendMap(OrderRequest *curr_order)
{
	// * Get the order split size
	std::uint32_t split_size =
		this->getCurrOrderSize(curr_order->symbol, curr_order->direction);

	// * Doubling split_size
	split_size = split_size*2;

	std::uint32_t split_qty = 0;

	// * Split the order into multiple smaller orders
	split_qty = std::min(curr_order->ord_qty, (std::int32_t)split_size);
	OrderInfo order_info(curr_order->symbol, curr_order->ord_type,
						 curr_order->direction, curr_order->ord_px, 0, split_qty,
						 split_qty, this->ord_counter, CUtility::getEpochTime(),
						 true, false, false, false, "", 0, false, 0, 0, 0,
						 curr_order->balte_id);
	curr_order->ord_qty -= split_qty;

	data_mngr->insertOrderInfo(*curr_order);
	data_mngr->insertOrderInfo(order_info);

	this->ord_counter = data_mngr->updateOrderCounter(this->ord_counter);
	logger->lock_info(fmt::format("getOrderFromSendMap : Acquiring ord_hist lock for TradeID: {} and generated order with OrderID: {} for {} lots out of a total of {} lots", curr_order->balte_id, order_info.exec_ord_num, split_qty, curr_order->ord_qty));
	this->ord_hist_mtx.lock();
	logger->lock_info(fmt::format("getOrderFromSendMap : Acquired ord_hist lock for TradeID: {} and generated order with OrderID: {} for {} lots out of a total of {} lots", curr_order->balte_id, order_info.exec_ord_num, split_qty, curr_order->ord_qty));
	this->ord_hist[order_info.exec_ord_num] = order_info;
	this->ord_hist_mtx.unlock();
	logger->lock_info(fmt::format("getOrderFromSendMap : Released ord_hist lock for TradeID: {} and generated order with OrderID: {} for {} lots out of a total of {} lots", curr_order->balte_id, order_info.exec_ord_num, split_qty, curr_order->ord_qty));
	return order_info;
}

/**
 * @brief Dequeue orders received from CReceiver and perform netoff
 * and order splitting operations
 *
 */
void COrderManager::processEntryRequests()
{
	while (true)
	{	
		this->heartbeat_queue_mtx.lock();
		logger->lock_info("processEntryRequests : Acquiring lock on heartbeat_queue_mtx");
		logger->kafka_heartbeat_log("processEntryRequests");
		logger->lock_info("processEntryRequests : releasing lock on heartbeat_queue_mtx");
		this->heartbeat_queue_mtx.unlock();
		
		int curr_ts = CUtility::getEpochTime();
		if (curr_ts % 5 == 0)
			this->checkForTimedOutOrders();

		if (!(this->order_queue.empty()))
		{
			OrderRequest curr_order = this->popOrderRequest();
			curr_order = this->netoffSymbol(curr_order);
			this->addOrderToSendMap(curr_order);
		}
		this->sendNewOrders();
		usleep(250000);
	}
}

/**
 * @brief Pop symbolwise orders from orders_to_send_map and send to
 * CSender
 *
 */
void COrderManager::sendNewOrders()
{
	if (!(this->orders_to_send_map.empty()))
	{
		logger->lock_info("sendNewOrders : Acquiring orders_to_send_map_mtx orders");
		this->orders_to_send_map_mtx.lock();
		OrderRequest *curr_order;
		std::string curr_symbol = "";
		for (auto mapping : this->orders_to_send_map)
		{
			curr_symbol = mapping.first;
			if (orders_to_send_map[curr_symbol].empty())
				continue;
			curr_order = &(this->orders_to_send_map[curr_symbol].front());

			logger->lock_info(fmt::format("sendNewOrders : Acquiring curr_exec_symbol lock for TradeID: {}", curr_order->balte_id));
			this->curr_exec_symbols_mtx.lock();
			bool curr_symbol_not_being_executed = (!(curr_exec_symbols.contains(curr_symbol)) || (curr_exec_symbols[curr_symbol] == 0));
			this->curr_exec_symbols_mtx.unlock();
			logger->lock_info(fmt::format("sendNewOrders : Released curr_exec_symbol lock lock for TradeID: {}", curr_order->balte_id));			
			if (curr_symbol_not_being_executed){	
				
				OrderInfo order_info = this->getOrderFromSendMap(curr_order);
				if (curr_order->ord_qty == 0)
					this->orders_to_send_map[curr_symbol].pop_front();
				
				logger->lock_info(fmt::format("sendNewOrders : Acquiring curr_exec_symbol lock for TradeID: {} for setting order quantity of OrderID: {}", curr_order->balte_id, order_info.exec_ord_num));
				this->curr_exec_symbols_mtx.lock();
				curr_exec_symbols[curr_symbol] = order_info.ord_qty;
				this->curr_exec_symbols_mtx.unlock();
				logger->lock_info(fmt::format("sendNewOrders : Released curr_exec_symbol lock for TradeID: {} for setting order quantity of OrderID: {}", curr_order->balte_id, order_info.exec_ord_num));
				
				std::int32_t mqs = 0;
				logger->lock_info(fmt::format("sendNewOrders : Acquiring modif_queue lock for TradeID: {} and generated OrderID: {}", curr_order->balte_id, order_info.exec_ord_num));
				this->modif_queue_mtx.lock();
				this->modif_queue.push(order_info.exec_ord_num);
				mqs = modif_queue.size();
				this->modif_queue_mtx.unlock();
				logger->lock_info(fmt::format("sendNewOrders : Released modif_queue lock for TradeID: {} and generated OrderID: {}", curr_order->balte_id, order_info.exec_ord_num));

				logger->ord_info(fmt::format("sendNewOrders : Pushing OrderID: {} to modif_queue having current size: {}. Set "
											 "curr_exec_symbols qty for {} = {}",
											 order_info.exec_ord_num, mqs, order_info.symbol,
											 order_info.ord_qty));
			}	
			
		}
		this->orders_to_send_map_mtx.unlock();
		logger->lock_info("sendNewOrders : Released orders_to_send_map_mtx lock");
	}
}

void COrderManager::processPendingRequests()
{
	std::uint32_t curr_ts;
	bool modif_push_flag = false;
	bool send_push_flag = false;
	while (true)
	{
		this->heartbeat_queue_mtx.lock();
		logger->lock_info("processPendingRequests : Acquiring lock on heartbeat_queue_mtx");
		logger->kafka_heartbeat_log("processPendingRequests");
		logger->lock_info("processPendingRequests : released lock on heartbeat_queue_mtx");
		this->heartbeat_queue_mtx.unlock();
		
		while (!(this->modif_queue.empty()))
		{
			logger->lock_info("processPendingRequests : Acquiring ord_hist lock");
			this->ord_hist_mtx.lock();
			logger->lock_info("processPendingRequests : Acquired ord_hist lock");

			send_push_flag = false;
			modif_push_flag = false;
			
			std::int32_t mqs = 0;
			logger->lock_info("processPendingRequests : Acquiring modif_queue lock");
			this->modif_queue_mtx.lock();
			curr_ts = CUtility::getEpochTime();
			// TODO : lock packets only
			std::int32_t exec_ord_num = this->modif_queue.front();
			this->modif_queue.pop();
			mqs = modif_queue.size();

			logger->lock_info(fmt::format("processPendingRequests : Acquired modif_queue lock for OrderID: {}", exec_ord_num));
			logger->ord_info(fmt::format("processPendingRequests : Popped OrderID: {} from modif_queue having current size: {}", exec_ord_num, mqs));

			auto pack_ptr = &(this->ord_hist[exec_ord_num]);
			if (pack_ptr->is_new)
			{
				// * Send a new order
				if (pack_ptr->send_ts <= curr_ts)
				{
					this->sender->pushOrderInfo(*pack_ptr);
				}
				else
				{
					modif_push_flag = true;
				}
			}
			else if ((pack_ptr->is_reject) || (pack_ptr->is_cancel))
			{
				if (pack_ptr->is_reject)
					logger->ord_info(
						fmt::format("processPendingRequests : Order {} has been rejected with msg "
									"{}. Dropping from modification queue",
									pack_ptr->exec_ord_num, pack_ptr->rej_reason));
				else if (pack_ptr->is_cancel)
					logger->ord_info(
						fmt::format("processPendingRequests : Order {} has been cancelled. "
									"Dropping from modification queue",
									pack_ptr->exec_ord_num));
			}
			else if (pack_ptr->ord_qty == 0)
			{
				pack_ptr->is_new = false;
				pack_ptr->is_modif = false;
				logger->ord_info(fmt::format(
					"COrderManager : Order {} has been executed completely.",
					pack_ptr->exec_ord_num));
			}
			else if (pack_ptr->is_modif)
			{
				if (pack_ptr->send_ts <= curr_ts)
				{
					logger->ord_info(fmt::format("processPendingRequests : Preparing to send "
												 "modification for Order {} with {} lots",
												 pack_ptr->exec_ord_num,
												 pack_ptr->ord_qty));
					std::int32_t curr_px = data_rdr->readSymbol(
						pack_ptr->symbol, (pack_ptr->direction == 1) ? "bid1" : "ask1");
					if (curr_px == -1)
					{
						// TODO : Handle this situation
						logger->ord_error(fmt::format("processPendingRequests : Current price "
													  "couldn't be fetched for symbol {}",
													  pack_ptr->symbol));
						modif_push_flag = true;
					}
					else
					{

						int additional_price_tick = 1;
						if (pack_ptr->symbol.find("CRUDEOIL") != std::string::npos) {
							additional_price_tick = 2;
						}
						curr_px = curr_px + ((pack_ptr->direction == 1) ? 1 : -1) * additional_price_tick * price_tick[pack_ptr->symbol];

						// corner case - ask
						if(curr_px <= 0){
							curr_px = price_tick[pack_ptr->symbol];
						}
						
						// Round to Tick size 
						curr_px = ((curr_px + price_tick[pack_ptr->symbol]/2) / price_tick[pack_ptr->symbol])*price_tick[pack_ptr->symbol];
						
						// send modification only when prices are changed
						if(curr_px == (pack_ptr->ord_px)){
							modif_push_flag = true;
						}
						else{
							pack_ptr->ord_px = curr_px;
							send_push_flag = true;
						}
					}
				}
				else
				{
					modif_push_flag = true;
				}
			}
			else
			{
				// Raise errors
			}
			if (send_push_flag)
			{
				this->sender->pushOrderInfo(*pack_ptr);
				logger->ord_info(fmt::format("processPendingRequests : Pushed modification for "
											 "order {} with {} lots into send_queue",
											 pack_ptr->exec_ord_num,
											 pack_ptr->ord_qty));
			}
			else if (modif_push_flag)
			{
				this->modif_queue.push(pack_ptr->exec_ord_num);
				mqs = modif_queue.size();
				logger->ord_info(fmt::format("processPendingRequests : Pushed OrderID: {} to the modif_queue having current size: {}", pack_ptr->exec_ord_num, mqs));
				this->heartbeat_queue_mtx.lock();
				logger->lock_info("processPendingRequests : Acquiring lock on heartbeat_queue_mtx");
				logger->kafka_heartbeat_log("processPendingRequests");
				logger->lock_info("processPendingRequests : released lock on heartbeat_queue_mtx");
				this->heartbeat_queue_mtx.unlock();
			}
			this->ord_hist_mtx.unlock();
			logger->lock_info("processPendingRequests : Released ord_hist lock");
			this->modif_queue_mtx.unlock();
			logger->lock_info(fmt::format("processPendingRequests : Released modif_queue lock for OrderID: {}", exec_ord_num));
			usleep(500000);
		}
		usleep(500000);
	}
}

void COrderManager::processOrdResponses(
	const aurora::OrderPacket &order_packet)
{
	std::int32_t mqs = 0;
	this->updateSentOrderTime(order_packet.exec_ord_num(), false);
	logger->lock_info(fmt::format("processOrdResponses : Acquiring ord_hist lock for OrderID: {}", order_packet.exec_ord_num()));
	std::lock_guard<std::mutex> guard1(this->ord_hist_mtx);
	auto ord_ptr = this->ord_hist.find(std::stoull(order_packet.exec_ord_num()));
	if (ord_ptr != this->ord_hist.end())
	{
		auto pack_ptr = &(ord_ptr->second);
		logger->ord_info(
			fmt::format("COrderManager : Received order status from CTCL for id {}",
						pack_ptr->exec_ord_num));
		// logger->ord_info(fmt::format("COrderManager : Waiting for order {} lock",
		// pack_ptr->exec_ord_num)); pack_ptr->ord_mtx.lock();
		// logger->ord_info(fmt::format("COrderManager : Acquired order {} lock",
		// pack_ptr->exec_ord_num));
		if (!((order_packet.status() == ENT_REJC) ||
			  (order_packet.status() == MOD_REJC) ||
			  (order_packet.status() == CAN_REJC) ||
			  (order_packet.status() == NOT_FOUND_) ||
			  (order_packet.status() == ENT_SENT) ||
			  (order_packet.status() == MOD_SENT) ||
			  (order_packet.status() == CAN_SENT)))
		{
			if (order_packet.status() == CAN_CONF)
			{
				pack_ptr->is_new = false;
				pack_ptr->is_modif = false;
				pack_ptr->is_cancel = true;
				logger->ord_info(fmt::format(
					"COrderManager : Order cancellation confirmed for id {}, {}, {}",
					order_packet.kivi_ord_num(), order_packet.exec_ord_num(),
					order_packet.exch_ord_num()));
				data_mngr->updateOrderInfo(ord_ptr->second);
				logger->ord_info(fmt::format("COrderManager : DB updated after "
											 "cancellation confirmation for id {}",
											 pack_ptr->exec_ord_num));
				logger->kafka_log(fmt::format(
					"{},{},{},{},{},{}", pack_ptr->balte_id, pack_ptr->symbol,
					pack_ptr->ord_qty, (pack_ptr->direction == 1) ? "B" : "S",
					pack_ptr->ord_qty, (pack_ptr->ord_px) / 100.0));
				// Send log in proto format
				ExecutionLogs::BaseLog cancel_base_log = CUtility::getBaseLogObject();
				ExecutionLogs::CancellationLog* cancellation_log = cancel_base_log.mutable_cancellation_log();
				cancellation_log->set_order_id(std::stoi(pack_ptr->balte_id));
				cancellation_log->set_contract(pack_ptr->symbol);
				cancellation_log->set_order_qty(pack_ptr->ord_qty);
				cancellation_log->set_executed_qty(pack_ptr->ord_qty);
				cancellation_log->set_buy_sell((pack_ptr->direction == 1) ? ExecutionLogs::BuySell::buy : ExecutionLogs::BuySell::sell);
				cancellation_log->set_order_price(pack_ptr->ord_px);
				logger->kafka_send_proto(cancel_base_log);

				logger->ord_info(fmt::format(
					"processOrdResponses : Sent cancellation confirmation of OrderID: {} to kafka",
					pack_ptr->exec_ord_num));
				logger->lock_info(fmt::format("processOrdResponses : Acquiring lock on curr_exec_symbols for setting it 0 for OrderID: {}", pack_ptr->exec_ord_num));
				std::lock_guard<std::mutex> guard2(this->curr_exec_symbols_mtx);
				if (this->curr_exec_symbols.contains(order_packet.symbol()))
				{
					this->curr_exec_symbols[order_packet.symbol()] = 0;
					logger->ord_info(fmt::format("processOrdResponses : Set curr_exec_symbols "
												 "= 0 on order cancellation for OrderID: {}, {}",
												 pack_ptr->exec_ord_num,
												 order_packet.symbol()));
				}
				else
				{
					logger->ord_warn(
						fmt::format("COrderManager : Order symbol {} not found in "
									"curr_exec_symbols table, packet ID {}",
									order_packet.symbol(), pack_ptr->exec_ord_num));
				}
				logger->lock_info(fmt::format("processOrdResponses : Released lock on curr_exec_symbols for setting it to 0 for OrderID: {}", pack_ptr->exec_ord_num));
			}
			else
			{
				int old_entry_px = pack_ptr->ord_px;
				pack_ptr->ord_px = order_packet.order_price();
				pack_ptr->ord_conf_ts = order_packet.order_ts();
				// pack_ptr->send_ts = CUtility::getEpochTime() +
				// modification_timer;
				struct tm curr_ts = CUtility::getCurrTimeStamp();
				if ((curr_ts.tm_hour >= 23) && (curr_ts.tm_min >= 45))
					pack_ptr->send_ts =
						CUtility::getEpochTime() + 5;
				else
					pack_ptr->send_ts =
						CUtility::getEpochTime() + std::stoi(CUtility::getConfig("Limits", "modif_delay_lw_lmt", "10"));

				logger->ord_info(fmt::format(
					"COrderManager : Received order entry/modif confirmation for id "
					"{}. Preparing to send modification.",
					pack_ptr->exec_ord_num));
				if (!(order_packet.status() == CAN_CONF) && (pack_ptr->ord_qty > 0))
				{
					pack_ptr->is_new = false;
					pack_ptr->is_modif = true;
					logger->lock_info(fmt::format(
						"processOrdResponses : Waiting for modif_queue lock for OrderID: {}",
						pack_ptr->exec_ord_num));
					std::lock_guard<std::mutex> guard3(this->modif_queue_mtx);
					logger->lock_info(fmt::format(
						"processOrdResponses : Acquired modif_queue lock for OrderID: {}",
						pack_ptr->exec_ord_num));
					this->modif_queue.push(pack_ptr->exec_ord_num);
					mqs = modif_queue.size();
					logger->lock_info(fmt::format(
						"processOrdResponses : Released modif_queue lock for OrderID: {}",
						pack_ptr->exec_ord_num));
					logger->ord_info(fmt::format("processOrdResponses : Sent modification and pushed to modif_queue having current size: {} for "
												 "OrderID {} : ({}, {}, {}, {} lots)",
												 mqs, pack_ptr->exec_ord_num, pack_ptr->symbol,
												 pack_ptr->ord_px, pack_ptr->direction,
												 pack_ptr->ord_qty));
				}

				// TODO : When the calls are made async, pass the packets by value
				data_mngr->updateOrderInfo(ord_ptr->second);
				if (order_packet.status() == ENT_CONF)
				{
					logger->ord_info(fmt::format(
						"Order entry confirmed for id {}, {}, {}",
						order_packet.kivi_ord_num(), order_packet.exec_ord_num(),
						order_packet.exch_ord_num()));
					logger->ord_info(fmt::format(
						"COrderManager : DB updated after entry confirmation for id {}",
						pack_ptr->exec_ord_num));
					try
					{
						this->updateMarketSnapForLogging(order_packet.symbol());
						logger->kafka_log(fmt::format(
							"{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{"
							"14},{"
							"15},"
							"{16},{17}",
							order_packet.exec_ord_num(),					   // order ID
							((order_packet.direction() == 1) ? "B" : "S"),	   // buy/sell
							ord_ptr->second.ord_qty + ord_ptr->second.trd_qty, // order qty
							ord_ptr->second.balte_id,						   // balte trade ID
							order_packet.symbol(),							   // symbol
							((order_packet.direction() == 1) ? "B" : "S"),	   // buy/sell
							ord_ptr->second.ord_qty + ord_ptr->second.trd_qty, // order qty
							order_packet.order_price(),						   // order px
							this->market_snap_log[2],						   // cons volume
							std::stoi(this->market_snap_log[7]) / 100.0,	   // bid1
							std::stoi(this->market_snap_log[22]) / 100.0,	   // ask1
							std::stoi(this->market_snap_log[3]) / 100.0,	   // ltp
							this->market_snap_log[0],						   // last traded time
							this->getTotalBuyQtyForLog(),					   // total buy qty
							this->getTotalSellQtyForLog(),					   // total sell qty
							0,   // vwap
							this->market_snap_log[0],						   // exchange broadcast time
							this->market_snap_log[0]						   // system receive time
							));
						
						// Send log in proto format
						google::protobuf::Timestamp order_book_timestamp = CUtility::TimeStringToTimestamp(this->market_snap_log[0]);
						ExecutionLogs::BaseLog sent_order_base_log = CUtility::getBaseLogObject();
						ExecutionLogs::SendOrderLog* sent_order_log = sent_order_base_log.mutable_send_order_log();
						sent_order_log->set_order_id(std::stoi(order_packet.exec_ord_num()));
						sent_order_log->set_buy_sell((order_packet.direction() == 1) ? ExecutionLogs::BuySell::buy : ExecutionLogs::BuySell::sell);
						sent_order_log->set_order_qty(ord_ptr->second.ord_qty + ord_ptr->second.trd_qty);
						sent_order_log->set_trade_id(std::stoll(ord_ptr->second.balte_id));
						sent_order_log->set_contract(order_packet.symbol());
						sent_order_log->set_trade_buy_sell((order_packet.direction() == 1) ? ExecutionLogs::BuySell::buy : ExecutionLogs::BuySell::sell);
						sent_order_log->set_trade_qty(ord_ptr->second.ord_qty + ord_ptr->second.trd_qty);
						sent_order_log->set_order_price(order_packet.order_price());
						ExecutionLogs::OrderBookLog* order_book = sent_order_log->mutable_order_book(); 
						order_book->set_buy_price_1(std::stoi(this->market_snap_log[7]));
						order_book->set_sell_price_1(std::stoi(this->market_snap_log[22]));
						order_book->set_last_traded_price(std::stoi(this->market_snap_log[3]));
						google::protobuf::Timestamp* ltt = order_book->mutable_last_traded_time();
						ltt->CopyFrom(order_book_timestamp);
						order_book->set_total_buy_qty(std::stoll(this->getTotalBuyQtyForLog()));
						order_book->set_total_sell_qty(std::stoll(this->getTotalSellQtyForLog()));
						order_book->set_avg_traded_price(0);
						google::protobuf::Timestamp* exch_send_time = order_book->mutable_exch_send_time();
						exch_send_time->CopyFrom(order_book_timestamp);
						google::protobuf::Timestamp* system_receive_time = order_book->mutable_system_receive_time();
						system_receive_time->CopyFrom(order_book_timestamp);

						logger->kafka_send_proto(sent_order_base_log);

						logger->kafka_log(fmt::format(
							"{0},{1},{2},{3},{4},{5},{6},{7},{8},{9}",
							"ExchangeOrderConfirmation",
							order_packet.exec_ord_num(),
							0,
							0,
							0,
							((order_packet.direction() == 1) ? "B" : "S"),
							1,
							order_packet.exch_ord_num(),
							0,
							"Success"));
						
						// Send log in proto format
						ExecutionLogs::BaseLog client_order_base_log = CUtility::getBaseLogObject();
						ExecutionLogs::ClientOrderMsg* client_order_log = client_order_base_log.mutable_order_confirmation();
						client_order_log->set_client_order_id(order_packet.exec_ord_num());
						client_order_log->set_exchange_order_id(std::to_string(order_packet.exch_ord_num()));
						client_order_log->set_buy_sell((order_packet.direction() == 1) ? ExecutionLogs::BuySell::buy : ExecutionLogs::BuySell::sell);
						logger->kafka_send_proto(client_order_base_log);

					}
					catch (const std::exception &e)
					{
						std::cerr << e.what() << '\n';
						logger->raise_error(e.what());
					}
				}
				else if (order_packet.status() == MOD_CONF)
				{
					logger->ord_info(fmt::format(
						"Order modification confirmed for id {}, {}, {}",
						order_packet.kivi_ord_num(), order_packet.exec_ord_num(),
						order_packet.exch_ord_num()));
					logger->ord_info(fmt::format("COrderManager : DB updated after "
												 "modification confirmation for id {}",
												 pack_ptr->exec_ord_num));
					try
					{
						this->updateMarketSnapForLogging(order_packet.symbol());
						logger->kafka_log(fmt::format(
							"{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{"
							"14}",
							order_packet.exec_ord_num(),					   // order ID
							order_packet.symbol(),							   // symbol
							old_entry_px,									   // old_ord_px
							std::stoi(this->market_snap_log[7]) / 100.0,	   // bid1
							std::stoi(this->market_snap_log[22]) / 100.0,	   // ask1
							order_packet.order_price(),						   // order px
							ord_ptr->second.ord_qty + ord_ptr->second.trd_qty, // order qty
							this->market_snap_log[2],						   // cons volume
							std::stoi(this->market_snap_log[3]) / 100.0,	   // ltp
							this->market_snap_log[0],						   // last traded time
							this->getTotalBuyQtyForLog(),					   // total buy qty
							this->getTotalSellQtyForLog(),					   // total sell qty
							0,   // vwap
							this->market_snap_log[0],						   // exchange broadcast time
							this->market_snap_log[0]						   // system receive time
							));
						// Send log in proto format
						google::protobuf::Timestamp order_book_timestamp = CUtility::TimeStringToTimestamp(this->market_snap_log[0]);
						ExecutionLogs::BaseLog modification_base_log = CUtility::getBaseLogObject();
						ExecutionLogs::ModificationLog* modification_log = modification_base_log.mutable_modification_log();
						modification_log->set_order_id(std::stoi(order_packet.exec_ord_num()));
						modification_log->set_buy_sell((order_packet.direction() == 1) ? ExecutionLogs::BuySell::buy : ExecutionLogs::BuySell::sell);
						modification_log->set_remaining_qty(ord_ptr->second.ord_qty);
						modification_log->set_contract(order_packet.symbol());
						modification_log->set_old_order_price(old_entry_px);
						modification_log->set_modified_order_price(order_packet.order_price());
						ExecutionLogs::OrderBookLog* order_book = modification_log->mutable_order_book();
						order_book->set_buy_price_1(std::stoi(this->market_snap_log[7]));
						order_book->set_sell_price_1(std::stoi(this->market_snap_log[22]));
						order_book->set_last_traded_price(std::stoi(this->market_snap_log[3]));
						google::protobuf::Timestamp* ltt = order_book->mutable_last_traded_time();
						ltt->CopyFrom(order_book_timestamp);
						order_book->set_total_buy_qty(std::stoll(this->getTotalBuyQtyForLog()));
						order_book->set_total_sell_qty(std::stoll(this->getTotalSellQtyForLog()));
						order_book->set_avg_traded_price(0);
						google::protobuf::Timestamp* exch_send_time = order_book->mutable_exch_send_time();
						exch_send_time->CopyFrom(order_book_timestamp);
						google::protobuf::Timestamp* system_receive_time = order_book->mutable_system_receive_time();
						system_receive_time->CopyFrom(order_book_timestamp);
						logger->kafka_send_proto(modification_base_log);
					}
					catch (const std::exception &e)
					{
						std::cerr << e.what() << '\n';
						logger->raise_error(e.what());
					}
				}
			}
		}
		else
		{
			std::string rej_reason;
			switch (order_packet.status())
			{
			case ENT_SENT:
			{
				rej_reason = fmt::format("[Teams Alert] COrderManager : Entry not "
										 "confirmed by exchange. Manual "
										 "intervention required : {0}",
										 order_packet.exec_ord_num());
				break;
			}
			case MOD_SENT:
			{
				rej_reason = fmt::format("[Teams Alert] COrderManager : Modification "
										 "not confirmed by exchange. "
										 "Manual intervention required : {0}",
										 order_packet.exec_ord_num());
				break;
			}
			case CAN_SENT:
			{
				rej_reason = fmt::format("[Teams Alert] COrderManager : Cancellation "
										 "not confirmed by exchange. "
										 "Manual intervention required : {0}",
										 order_packet.exec_ord_num());
				break;
			}
			default:
			{
				rej_reason = order_packet.rej_reason();
				break;
			}
			}
			pack_ptr->is_reject = true;
			pack_ptr->rej_reason = rej_reason;
			logger->ord_info(fmt::format(
				"COrderManager : Received order rejection for id {} with msg {}",
				pack_ptr->exec_ord_num, pack_ptr->rej_reason));
			
			if (rej_reason.substr(0, 6) == "[Teams"){
				
				this->heartbeat_queue_mtx.lock();
				logger->lock_info("processOrdResponses : Acquiring lock on heartbeat_queue_mtx");
				logger->kafka_heartbeat_log("Order Rejection");
				logger->lock_info("processOrdResponses : released lock on heartbeat_queue_mtx");
				this->heartbeat_queue_mtx.unlock();
				
			}
						
			if (order_packet.status() == ENT_REJC &&
				(rej_reason.substr(0, 8) == "DPRLIMIT"))
			{
				logger->ord_info(fmt::format(
					"processOrdResponses : Entry Rejected for {0} : DPR Limit : {1}",
					pack_ptr->exec_ord_num, rej_reason));

				// * Create new Order with updated timestamp and push to send_order_map
				int delay =
					std::stoi(CUtility::getConfig("DPR", "cooldown_period", "900"));
				OrderInfo new_order = *pack_ptr;
				new_order.exec_ord_num = this->ord_counter;
				new_order.send_ts = CUtility::getEpochTime() + delay;
				new_order.is_reject = false;
				new_order.rej_reason = "";
				new_order.is_new = true;
				new_order.is_modif = false;
				new_order.is_cancel = false;
				this->ord_counter = data_mngr->updateOrderCounter(this->ord_counter);
				logger->ord_info(fmt::format(
					"Recreating entry : {0} against {1} : {2}, {3}, "
					"{4}, {5}",
					new_order.exec_ord_num, pack_ptr->exec_ord_num, new_order.balte_id,
					new_order.symbol, new_order.ord_qty, new_order.direction));
				this->ord_hist[new_order.exec_ord_num] = new_order;
				{
					logger->lock_info(fmt::format("processOrdResponses : Acquiring lock on modif_queue for recreated entry {} against {}", new_order.exec_ord_num, pack_ptr->exec_ord_num));
					std::lock_guard<std::mutex> guard4(this->modif_queue_mtx);
					this->modif_queue.push(new_order.exec_ord_num);
					mqs = modif_queue.size();
					logger->lock_info(fmt::format("processOrdResponses : Released lock on modif_queue for recreated entry {} against {}", new_order.exec_ord_num, pack_ptr->exec_ord_num));
					logger->ord_info(fmt::format("processOrdResponses : Pushed recreated entry {} against {} to modif_queue having current size: {}", new_order.exec_ord_num, pack_ptr->exec_ord_num, mqs));
				}
				data_mngr->insertOrderInfo(new_order);
			}
			else if (order_packet.status() == MOD_REJC)
			{
				// * Re-send Modifications only for DPR LIMIT or for error msg containing RESEND
				if (rej_reason.substr(0, 8) == "DPRLIMIT")
				{
					pack_ptr->is_reject = false;
					pack_ptr->is_new = false;
					pack_ptr->is_modif = true;
					pack_ptr->is_cancel = false;

					int delay =
						std::stoi(CUtility::getConfig("DPR", "cooldown_period", "900"));
					pack_ptr->send_ts = CUtility::getEpochTime() + delay;
					logger->ord_warn(fmt::format(
						"processOrdResponses : Modification rejected for DPR Limit : {0} "
						": {1}, placing next modification after {2} seconds",
						pack_ptr->exec_ord_num, rej_reason, delay));
					logger->lock_info(fmt::format("processOrdResponses : Acquiring lock on modif_queue for OrderID: {}", pack_ptr->exec_ord_num));
					std::lock_guard<std::mutex> guard5(this->modif_queue_mtx);
					this->modif_queue.push(pack_ptr->exec_ord_num);
					mqs = modif_queue.size();
					logger->lock_info(fmt::format("processOrdResponses : Released lock on modif_queue for OrderID: {}", pack_ptr->exec_ord_num));
					logger->ord_info(fmt::format("processOrdResponses : Pushed next modification for OrderID: {} to modif_queue having current size: {}", pack_ptr->exec_ord_num, mqs));
				}
				else if (rej_reason.substr(0, 6) == "RESEND")
				{
					pack_ptr->is_reject = false;
					pack_ptr->is_new = false;
					pack_ptr->is_modif = true;
					pack_ptr->is_cancel = false;

					//log re-modification request
					logger->ord_info(
						fmt::format(
							"processOrdResponses : Order modification rejected for id {}, {}, "
							"{}. Sending re-modification",
							order_packet.kivi_ord_num(), order_packet.exec_ord_num(),
							order_packet.exch_ord_num()
						)
					);
					
					pack_ptr->send_ts = CUtility::getEpochTime() + std::stoi(CUtility::getConfig("Limits", "modif_delay_lw_lmt", "10"));

					logger->lock_info(fmt::format("processOrdResponses : Acquiring lock on modif_queue for resending OrderID: {}", pack_ptr->exec_ord_num));
					std::lock_guard<std::mutex> guard6(this->modif_queue_mtx);
					this->modif_queue.push(pack_ptr->exec_ord_num);
					mqs = modif_queue.size();
					logger->lock_info(fmt::format("processOrdResponses : Released lock on modif_queue for resending OrderID: {}", pack_ptr->exec_ord_num));
					logger->ord_info(fmt::format("processOrdResponses : Pushed re-modification for OrderID: {} to modif_queue having current size: {}", pack_ptr->exec_ord_num, mqs));
				}
				else
				{
					// Set values
					pack_ptr->is_reject = true;
					pack_ptr->is_new = false;
					pack_ptr->is_modif = false;
					pack_ptr->is_cancel = false;

					//log rejection
					logger->ord_info(fmt::format(
						"processOrdResponses : Order modification rejected for id {}, {}, "
						"{}. Wont send modification",
						order_packet.kivi_ord_num(), order_packet.exec_ord_num(),
						order_packet.exch_ord_num()));
					
					// clear curr_exec_symbol
					logger->lock_info(fmt::format("processOrdResponses : Acquiring lock on curr_exec_symbols for setting it 0 for OrderID: {}", pack_ptr->exec_ord_num));
					std::lock_guard<std::mutex> guard7(this->curr_exec_symbols_mtx);
					if (this->curr_exec_symbols.contains(order_packet.symbol()))
					{
						this->curr_exec_symbols[order_packet.symbol()] = 0;
						logger->ord_info(
							fmt::format(
								"processOrdResponses : Set curr_exec_symbols "
								"= 0 on order modification rejection of {}, {}",
								pack_ptr->exec_ord_num,
								order_packet.symbol()
							)
						);
					}
					else
					{
						logger->ord_warn(
						fmt::format("processOrdResponses : Order symbol {} not found in "
						"curr_exec_symbols table, packet ID {}",
						order_packet.symbol(), pack_ptr->exec_ord_num));
					}
					logger->lock_info(fmt::format("processOrdResponses : Released lock on curr_exec_symbols for setting it 0 for OrderID: {}", pack_ptr->exec_ord_num));
				}
				// * Send cancellations on rejection
				// pack_ptr->is_new = false;
				// pack_ptr->is_modif = false;
				// pack_ptr->is_cancel = true;
				// pack_ptr->ord_mtx.unlock();
				// logger->ord_info(fmt::format(
				//     "Order modification rejected for id {}, {}, {}. Sending
				//     cancellation", order_packet.kivi_ord_num(),
				//     order_packet.exec_ord_num(), order_packet.ex_ord_num()));
				// this->sender->pushOrderInfo(*pack_ptr);

				logger->ord_info(fmt::format(
					"Order modification rejected for id {}, {}, {} with message : {}",
					order_packet.kivi_ord_num(), order_packet.exec_ord_num(),
					order_packet.exch_ord_num(), order_packet.rej_reason()));
			}
			else if ((order_packet.status() == ENT_REJC) ||
					 (order_packet.status() == ENT_SENT))
			{
				logger->ord_info(fmt::format("Order entry rejected for id {}, {}, {}",
											 order_packet.kivi_ord_num(),
											 order_packet.exec_ord_num(),
											 order_packet.exch_ord_num()));
				logger->ord_info(fmt::format(
					"COrderManager : Sent entry rejection to kafka for id {}",
					pack_ptr->exec_ord_num));

				logger->lock_info(
					fmt::format("processOrdResponses : Acquiring lock on curr_exec_symbols for setting it 0 for OrderID: {}", pack_ptr->exec_ord_num));
				std::lock_guard<std::mutex> guard8(this->curr_exec_symbols_mtx);
				if (this->curr_exec_symbols.contains(order_packet.symbol()))
				{
					this->curr_exec_symbols[order_packet.symbol()] = 0;
					logger->ord_info(fmt::format("processOrdResponses : Set curr_exec_symbols "
												 "= 0 on order entry rejection of {}, {}",
												 pack_ptr->exec_ord_num,
												 order_packet.symbol()));
				}
				else
				{
					logger->ord_warn(
						fmt::format("processOrdResponses : Order symbol {} not found in "
									"curr_exec_symbols table, packet ID {}",
									order_packet.symbol(), pack_ptr->exec_ord_num));
				}
				logger->lock_info(
					fmt::format("processOrdResponses : Released lock on curr_exec_symbols for setting it 0 for OrderID: {}", pack_ptr->exec_ord_num));
			}
			else if (order_packet.status() == CAN_REJC)
			{
				logger->ord_info(fmt::format(
					"Order cancellation rejected for id {}, {}, {}",
					order_packet.kivi_ord_num(), order_packet.exec_ord_num(),
					order_packet.exch_ord_num()));
				logger->ord_info(fmt::format(
					"COrderManager : Sent cancellation rejection to kafka for id {}",
					pack_ptr->exec_ord_num));
			}
			else if (order_packet.status() == NOT_FOUND_)
			{
				logger->ord_error(
					fmt::format("No Info available for order {0} with CTCL/Exchange",
								order_packet.exec_ord_num()));
				// * What all steps to be taken need to be implemented here
				// * If entry not received, send it
				// * If modification not received, send it
				// * If cancellation not received, send it
				// pack_ptr->is_reject = false;
				// pack_ptr->rej_reason = "";
				// if (pack_ptr->is_new)
				// {
				// 	// Place the entry again
				// 	// curr_exec_symbols[order_info.symbol] must be 0 here
				// 	this->sender->pushOrderInfo(*pack_ptr);
				// 	logger->ord_info(fmt::format(
				// 		"processOrdResponses : sendNewOrders Pushing order {}"
				// 		"again. Set curr_exec_symbols qty for {} = {}",
				// 		pack_ptr->exec_ord_num, pack_ptr->symbol,
				// pack_ptr->ord_qty));
				// }
				// else if (pack_ptr->is_modif)
				// {
				// 	// Send Modification again
				// 	this->modif_queue_mtx.lock();
				// 	this->modif_queue.push(pack_ptr->exec_ord_num);
				// 	this->modif_queue_mtx.unlock();
				// 	logger->ord_info(fmt::format(
				// 		"processOrdResponses : sending modification for order
				// {}" 		"again", 		pack_ptr->exec_ord_num));
				// }
				// else if (pack_ptr->is_cancel)
				// {
				// 	// send Cancellation again
				// 	pack_ptr->is_new = false;
				// 	pack_ptr->is_modif = false;
				// 	pack_ptr->is_cancel = true;
				// 	this->sender->pushOrderInfo(*pack_ptr);
				// 	logger->ord_info(fmt::format(
				// 		"processOrdResponses : sending cancellation for order
				// {}" 		"again", 		pack_ptr->exec_ord_num));
				// }
			}
			data_mngr->updateOrderInfo(*pack_ptr);
			// pack_ptr->ord_mtx.unlock();
			logger->lock_info(
				fmt::format("processOrdResponses : Released lock for OrderID: {}",
							pack_ptr->exec_ord_num));
			// data_mngr->deleteOrderInfo(*pack_ptr);
			// logger->ord_error(fmt::format("Order {} rejected with rejection message
			// {}", pack_ptr->exec_ord_num, pack_ptr->rej_reason));
		}
	}
	else
	{
		logger->ord_error(
			fmt::format("processOrdResponses : Order notification received for order {}. "
						"Corresponding order not found in order map",
						order_packet.exec_ord_num()));
	}
	logger->lock_info(fmt::format("processOrdResponses : Released ord_hist lock for OrderID: {}", order_packet.exec_ord_num()));
}

void COrderManager::processTrdResponses(
	const aurora::TradePacket &trade_packet)
{
	logger->lock_info(fmt::format("processTrdResponses : Acquiring ord_hist lock for OrderID: {}", trade_packet.exec_ord_num()));
	std::lock_guard<std::mutex> guard1(this->ord_hist_mtx);
	auto ord_ptr = this->ord_hist.find(std::stoi(trade_packet.exec_ord_num()));
	if (ord_ptr != this->ord_hist.end())
	{
		logger->ord_info(
			fmt::format("COrderManager : Received trade notification for id {}",
						ord_ptr->second.exec_ord_num));
		int total_trd_qty = ord_ptr->second.trd_qty;
		ord_ptr->second.is_trd = true;
		ord_ptr->second.trd_px = trade_packet.trade_price();
		ord_ptr->second.trd_qty = trade_packet.quantity();
		ord_ptr->second.ord_qty =
			std::max(ord_ptr->second.ord_qty - trade_packet.quantity(), 0);
		ord_ptr->second.disc_qty =
			std::max(ord_ptr->second.disc_qty - trade_packet.quantity(), 0);
		ord_ptr->second.trd_conf_ts = trade_packet.trade_ts();
		ord_ptr->second.is_trd = true;
		ord_ptr->second.exch_trd_num = trade_packet.trd_num();
		data_mngr->insertTradeInfo(ord_ptr->second);
		logger->ord_info(fmt::format("COrderManager : Updated DB tradebook with "
									 "trade notification for id {}",
									 ord_ptr->second.exec_ord_num));
		// * Hacky fix : trd table has to be filled with the current traded
		// quantity,
		// * but order history and order table has to be filled with cumulative trd
		// qty.
		ord_ptr->second.trd_qty += total_trd_qty;
		data_mngr->updateOrderInfo(ord_ptr->second);
		logger->ord_info(fmt::format("COrderManager : Updated DB orderbook with "
									 "trade notification for id {}",
									 ord_ptr->second.exec_ord_num));
		if (trade_packet.kivi_ord_num() != NETOFF_KIVI_ID)
		{
			logger->lock_info(fmt::format("processTrdResponses : Acquiring curr_exec_symbol lock for setting down qty for OrderID: {}", ord_ptr->second.exec_ord_num));
			std::lock_guard<std::mutex> guard2(this->curr_exec_symbols_mtx);
			if (this->curr_exec_symbols.contains(trade_packet.symbol()))
			{
				this->curr_exec_symbols[trade_packet.symbol()] -=
					std::min(trade_packet.quantity(),
							 this->curr_exec_symbols[trade_packet.symbol()]);
				logger->ord_info(
					fmt::format("processTrdResponses : Set curr_exec_symbols = {} on trade "
								"confirmation of {}, {}",
								this->curr_exec_symbols[trade_packet.symbol()],
								ord_ptr->second.exec_ord_num, trade_packet.symbol()));
			}
			else
			{
				logger->ord_warn(fmt::format("processTrdResponses : Trade symbol {} not found in "
											 "curr_exec_symbols table, packet ID {}",
											 trade_packet.symbol(),
											 ord_ptr->second.exec_ord_num));
			}
			logger->lock_info(fmt::format("processTrdResponses : Released curr_exec_symbol lock for setting down qty for OrderID: {}", ord_ptr->second.exec_ord_num));
		}
		this->updateMarketSnapForLogging(trade_packet.symbol());
		try
		{
			logger->kafka_log(fmt::format(
				"{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{"
				"15},"
				"{16},{17}",
				trade_packet.exec_ord_num(),					   // orderID -1
				ord_ptr->second.ord_qty + ord_ptr->second.trd_qty, // orderQty -2
				ord_ptr->second.balte_id,						   // tradeID -3
				"",												   // Trade/NetOff - 4
				trade_packet.symbol(),							   // OrderKey -5
				((trade_packet.direction() == 1) ? "B" : "S"),	   // BuySell -6
				trade_packet.quantity(),						   // Quantity -7
				(trade_packet.trade_price()) / 100.0,			   // executedPrice -8
				this->market_snap_log[2],						   // volume traded today -9
				std::stoi(this->market_snap_log[7]) / 100.0,	   // buy price1 -10
				std::stoi(this->market_snap_log[22]) / 100.0,	   // sell price1 -11
				std::stoi(this->market_snap_log[3]) / 100.0,	   // ltp - 12
				this->market_snap_log[0],						   // last traded time HH:mm::ss.fff -13
				this->getTotalBuyQtyForLog(),					   // total buy qty -14
				this->getTotalSellQtyForLog(),					   // total sell qty -15
				0,				  // avg traded price -16
				this->market_snap_log[0], // exchange broadcast time HH:mm:ss.fff - 17
				this->market_snap_log[0]  // system receive time HH:mm:ss.fff -18
				));

			// Send log in proto format
			google::protobuf::Timestamp order_book_timestamp = CUtility::TimeStringToTimestamp(this->market_snap_log[0]);
			ExecutionLogs::BaseLog execution_base_log = CUtility::getBaseLogObject();
			ExecutionLogs::ExecutionLog* execution_log = execution_base_log.mutable_execution_log();
			execution_log->set_order_id(std::stoi(trade_packet.exec_ord_num()));
			execution_log->set_buy_sell((trade_packet.direction() == 1) ? ExecutionLogs::BuySell::buy : ExecutionLogs::BuySell::sell);
			execution_log->set_order_qty(ord_ptr->second.ord_qty + ord_ptr->second.trd_qty);
			execution_log->set_trade_id(std::stoll(ord_ptr->second.balte_id));
			execution_log->set_netoff(false);
			execution_log->set_contract(trade_packet.symbol());
			execution_log->set_trade_qty(trade_packet.quantity());
			execution_log->set_executed_price(trade_packet.trade_price());
			ExecutionLogs::OrderBookLog* order_book = execution_log->mutable_order_book();
			order_book->set_buy_price_1(std::stoi(this->market_snap_log[7]));
			order_book->set_sell_price_1(std::stoi(this->market_snap_log[22]));
			order_book->set_last_traded_price(std::stoi(this->market_snap_log[3]));
		        google::protobuf::Timestamp* ltt = order_book->mutable_last_traded_time();
			ltt->CopyFrom(order_book_timestamp);
			order_book->set_total_buy_qty(std::stoll(this->getTotalBuyQtyForLog()));
			order_book->set_total_sell_qty(std::stoll(this->getTotalSellQtyForLog()));
			order_book->set_avg_traded_price(0);
			google::protobuf::Timestamp* exch_send_time = order_book->mutable_exch_send_time();
			exch_send_time->CopyFrom(order_book_timestamp);
			google::protobuf::Timestamp* system_receive_time = order_book->mutable_system_receive_time();
			system_receive_time->CopyFrom(order_book_timestamp);
			logger->kafka_send_proto(execution_base_log);

		}
		catch (const std::exception &e)
		{
			std::cerr << e.what() << '\n';
			logger->raise_error(e.what());
		}
		logger->ord_info(fmt::format(
			"COrderManager : Sent trade notification log to kafka for id {}",
			trade_packet.exec_ord_num()));
	}
	else
	{
		logger->ord_error(
			fmt::format("COrderManager : Trade notification received for order {}. "
						"Corresponding order not found in order map",
						trade_packet.exec_ord_num()));
	}
	logger->ord_info(fmt::format(
		"COrderManager : Trade notif received for balte id {}, {}, {}, {}, "
		"{}, {}, {} lots",
		((ord_ptr != this->ord_hist.end()) ? ord_ptr->second.balte_id
										   : "Undefined"),
		trade_packet.kivi_ord_num(), trade_packet.exec_ord_num(),
		trade_packet.symbol(), trade_packet.trade_price(),
		trade_packet.direction(), trade_packet.quantity()));
	logger->lock_info(fmt::format("processTrdResponses : Released ord_hist lock for OrderID: {}", trade_packet.exec_ord_num()));
}

/**
 * @brief Clear the pending orders map and re-populate from DB
 *
 */
void COrderManager::repopulatePendingOrders()
{
	// Clear the map
	this->ord_hist.clear();

	// Fetch pending orders from db
	std::vector<std::vector<std::string>> pendingOrdersVector;
	pendingOrdersVector = data_mngr->getPendingOrdersFromDB();

	// populate the pending orders map
	for (auto row : pendingOrdersVector)
	{
		OrderInfo o;
		o.symbol = row[0];
		o.ord_type = row[1];
		o.direction = std::stoi(row[2]);
		o.ord_px = std::stoi(row[3]);
		o.trig_px = std::stoi(row[4]);
		o.ord_qty = std::stoi(row[5]);
		o.disc_qty = std::stoi(row[6]);
		o.balte_id = row[7];
		o.exec_ord_num = std::stoi(row[8]);
		o.ord_conf_ts = CUtility::convertTSToEpoch(row[9], "%Y-%m-%d %H:%M:%S");
		o.is_new = CONVERT_STR_TO_BOOL(row[10]);
		o.is_modif = CONVERT_STR_TO_BOOL(row[11]);
		o.is_cancel = CONVERT_STR_TO_BOOL(row[12]);
		o.is_reject = CONVERT_STR_TO_BOOL(row[13]);
		o.is_trd = CONVERT_STR_TO_BOOL(row[14]);
		o.send_ts = CUtility::convertTSToEpoch(row[16], "%Y-%m-%d %H:%M:%S");
		o.trd_qty = std::stoi(row[17]);
		o.rej_reason = row[18];
		logger->lock_info(fmt::format("repopulatePendingOrders : Acquiring ord_hist lock for OrderID: {}", o.exec_ord_num));
		this->ord_hist_mtx.lock();
		this->ord_hist[o.exec_ord_num] = o;
		this->ord_hist_mtx.unlock();
		logger->lock_info(fmt::format("repopulatePendingOrders : Released ord_hist lock for OrderID: {}", o.exec_ord_num));
		if (o.is_reject)
			continue;
			
		logger->lock_info(fmt::format("repopulatePendingOrders : Acquiring curr_exec_symbol lock for OrderID: {}", o.exec_ord_num));
		this->curr_exec_symbols_mtx.lock();
		if (!(this->curr_exec_symbols.contains(o.symbol)))
			this->curr_exec_symbols[o.symbol] = 0;
		this->curr_exec_symbols[o.symbol] += o.ord_qty;
		this->curr_exec_symbols_mtx.unlock();
		logger->lock_info(fmt::format("repopulatePendingOrders : Released curr_exec_symbol lock for OrderID: {}", o.exec_ord_num));

		std::int32_t mqs = 0;
		logger->lock_info(fmt::format("repopulatePendingOrders : Acquiring modif_queue lock for OrderID: {}", o.exec_ord_num));
		this->modif_queue_mtx.lock();
		this->modif_queue.push(o.exec_ord_num);
		mqs = modif_queue.size();
		this->modif_queue_mtx.unlock();
		logger->lock_info(fmt::format("repopulatePendingOrders : Released modif_queue lock for OrderID: {}", o.exec_ord_num));
		logger->ord_info(fmt::format("repopulatePendingOrders : Added order ({}, {}, {}, {} "
									 "lots) to modif_queue having current size: {} from DB",
									 o.exec_ord_num, o.symbol, o.direction,
									 o.ord_qty, mqs));
	}

	pendingOrdersVector.clear();
	pendingOrdersVector = data_mngr->getBaLTEOrdersFromDB();
	for (auto row : pendingOrdersVector)
	{
		OrderRequest o;
		o.symbol = row[1];
		o.ord_type = row[2];
		o.direction = std::stoi(row[3]);
		o.ord_px = std::stoi(row[4]);
		o.ord_qty = std::stoi(row[5]);
		o.total_ord_qty = std::stoi(row[6]);
		o.balte_id = row[7];
		if (o.ord_qty > 0)
		{
			this->pushOrderRequest(o);
		}
	}
}

int COrderManager::setExecOrdNum(int new_exec_ord_num)
{
	this->ord_counter = new_exec_ord_num;
	return 0;
}

void COrderManager::updateMarketSnapForLogging(const std::string symbol)
{
	std::string data = data_rdr->readKey(symbol);
	market_snap_log.clear();
	CUtility::split(data, '|', market_snap_log);


	try{
		int str_len;

		// Removing Decimal in prices to convert prices in rupees to paise
		// removing decimal from qty and vol
		for(int index = 6; index < market_snap_log.size(); index += 3){
			str_len = market_snap_log[index].size();
			market_snap_log[index] = market_snap_log[index].substr(0,str_len-3);

			str_len = market_snap_log[index+1].size();
			market_snap_log[index+1] = market_snap_log[index+1].substr(0,str_len-3) + market_snap_log[index+1].substr(str_len-2);

			str_len = market_snap_log[index+2].size();
			market_snap_log[index+2] = market_snap_log[index+2].substr(0,str_len-3);
	}
	}
	catch (std::exception &ex)
	{
		logger->raise_error(fmt::format("market_snap_log update error : {}", ex.what()));
	}

	
	market_snap_log.resize(41, "");
	if (market_snap_log[0] != "")
	{
		market_snap_log[0] = market_snap_log[0].substr(11, 8) + ".000";
	}
	return;
}

std::string COrderManager::getAvgTrdPriceForLog()
{
	if (this->market_snap_log[40] != "" && this->market_snap_log[2] != "")
	{
		float turnover = std::stof(this->market_snap_log[40]);
		int qty = std::stoi(this->market_snap_log[2]);
		if (qty == 0)
			return "";
		return std::to_string(turnover / qty);
	}
	else
	{
		return "";
	}
}

std::string COrderManager::getTotalBuyQtyForLog()
{
	int qty = 0;
	for (int i = 0; i < 5; i++)
	{
		if (this->market_snap_log[6 + i * 3] != "")
		{
			qty += std::stoi(this->market_snap_log[6 + i * 3]);
		}
	}
	return std::to_string(qty);
}

std::string COrderManager::getTotalSellQtyForLog()
{
	int qty = 0;
	for (int i = 0; i < 5; i++)
	{
		if (this->market_snap_log[21 + i * 3] != "")
		{
			qty += std::stoi(this->market_snap_log[21 + i * 3]);
		}
	}
	return std::to_string(qty);
}

void COrderManager::updateSentOrderTime(std::string exec_ord_num, bool insert)
{
	logger->lock_info(fmt::format("updateSentOrderTime : Acquiring sent_order_time lock for OrderID: {}", exec_ord_num));
	std::lock_guard<std::mutex> guard1(this->sent_order_time_mtx);
	if (insert)
	{
		if (this->sent_order_time.contains(exec_ord_num))
		{
			logger->ord_warn(fmt::format(
				"No response from previous request for : {0}", exec_ord_num));
		}
		else
		{
			this->sent_order_time[exec_ord_num] =
				CUtility::getEpochTime(); // insert timestamp
			logger->ord_info(fmt::format("Request sent for : {0}", exec_ord_num));
		}
	}
	else
	{
		if (this->sent_order_time.contains(exec_ord_num))
		{
			// delete
			this->sent_order_time.erase(exec_ord_num);
			logger->ord_info(
				fmt::format("Response received for : {0}", exec_ord_num));
		}
		else
		{
			// error
			logger->ord_warn(fmt::format(
				"Unsolicited Response from exchange for : {0}", exec_ord_num));
		}
	}
	logger->lock_info(fmt::format("updateSentOrderTime : Released sent_order_time lock for OrderID: {}", exec_ord_num));
	return;
}

std::vector<std::string> COrderManager::getTimedOutOrders()
{
	std::vector<std::string> timed_out_orders;
	timed_out_orders.clear();
	logger->lock_info("getTimedOutOrders : Acquiring sent_order_time lock");
	this->sent_order_time_mtx.lock();
	uint32_t curr_time = CUtility::getEpochTime();
	if (!this->sent_order_time.empty())
	{
		for (auto i = this->sent_order_time.begin();
			 i != this->sent_order_time.end(); i++)
		{
			if (curr_time - i->second > 60)
			{
				timed_out_orders.push_back(i->first);
				logger->ord_warn(fmt::format("No response in last 60 seconds from "
											 "exchange for order request : {0}",
											 i->first));
			}
		}
	}
	this->sent_order_time_mtx.unlock();
	logger->lock_info("getTimedOutOrders : Released sent_order_time lock");
	return timed_out_orders;
}

void COrderManager::checkForTimedOutOrders()
{
	std::vector<std::string> timed_out_orders = this->getTimedOutOrders();
	if (!timed_out_orders.empty())
	{
		this->handleTimedOutOrders(timed_out_orders);
	}

	return;
}

void COrderManager::handleTimedOutOrders(
	std::vector<std::string> timed_out_orders)
{
	for (auto i : timed_out_orders)
	{
		try
		{
			this->sender->getOrdStatus(i);
		}
		catch (const std::exception &ex)
		{
			logger->ord_error(
				fmt::format("Error in fetching status for timed out order : {} ; {}",
							i, ex.what()));
		}
	}
	return;
}

void COrderManager::clearCurrExecSymbol(std::string symbol)
{
	logger->lock_info(fmt::format("clearCurrExecSymbol : Acquiring curr_exec_symbol lock for symbol: {}", symbol));
	this->curr_exec_symbols_mtx.lock();
	if (this->curr_exec_symbols.contains(symbol))
	{
		this->curr_exec_symbols[symbol] = 0;
		logger->ord_info(fmt::format(
			"clearCurrExecSymbol : Set curr_exec_symbols for {} = 0", symbol));
	}
	this->curr_exec_symbols_mtx.unlock();
	logger->lock_info(fmt::format("clearCurrExecSymbol : Released curr_exec_symbol lock for symbol: {}", symbol));
}

void COrderManager::manualCancellation(std::int32_t exec_ord_num)
{
	// * Check Ord History
	logger->lock_info(fmt::format("manualCancellation : Acquiring ord_hist lock for OrderID: {}", exec_ord_num));
	this->ord_hist_mtx.lock();
	if (ord_hist.contains(exec_ord_num))
	{
		auto pack_ptr = &ord_hist[exec_ord_num];
		if (!(pack_ptr->is_cancel))
		{
			pack_ptr->is_new = false;
			pack_ptr->is_modif = false;
			pack_ptr->is_cancel = true;
			pack_ptr->is_reject = false;
			pack_ptr->rej_reason = "";
			logger->ord_info(
				fmt::format("manualCancellation : Sending manual cancellation for OrderID: {}",
							exec_ord_num));
			this->sender->pushOrderInfo(*pack_ptr);
		}
	}
	else
	{
		logger->ord_error(
			fmt::format("manualCancellation : Order id {} not found in ord hist. "
						"Skipping manual cancellation",
						exec_ord_num));
	}
	this->ord_hist_mtx.unlock();
	logger->lock_info(fmt::format("manualCancellation : Released ord_hist lock for OrderID: {}", exec_ord_num));
}

void COrderManager::manualStartModification(std::int32_t exec_ord_num,
											std::int32_t ord_px,
											std::uint32_t ord_qty)
{
	logger->lock_info(fmt::format("manualStartModification : Acquiring ord_hist lock for OrderID: {}", exec_ord_num));
	this->ord_hist_mtx.lock();
	if (ord_hist.contains(exec_ord_num))
	{
		auto pack_ptr = &ord_hist[exec_ord_num];
		if (!(pack_ptr->is_cancel))
		{
			pack_ptr->is_new = false;
			pack_ptr->is_modif = true;
			pack_ptr->is_cancel = false;
			pack_ptr->is_reject = false;
			pack_ptr->rej_reason = "";
			if (ord_qty > 0)
				pack_ptr->ord_qty = ord_qty;
			logger->lock_info(fmt::format("manualStartModification : Acquiring curr_exec_symbol lock for OrderID: {} and setting it to {}", exec_ord_num, pack_ptr->ord_qty));
			this->curr_exec_symbols_mtx.lock();
			if (!(this->curr_exec_symbols.contains(pack_ptr->symbol)))
				;
			this->curr_exec_symbols[pack_ptr->symbol] = 0;
			this->curr_exec_symbols[pack_ptr->symbol] += pack_ptr->ord_qty;
			this->curr_exec_symbols_mtx.unlock();
			logger->lock_info(fmt::format("manualStartModification : Released curr_exec_symbol lock for OrderID: {} and setting it to {}", exec_ord_num, pack_ptr->ord_qty));
			std::int32_t mqs = 0;
			logger->lock_info(fmt::format("manualStartModification : Acquiring modif_queue lock for OrderID: {}", exec_ord_num));
			this->modif_queue_mtx.lock();
			this->modif_queue.push(exec_ord_num);
			mqs = modif_queue.size();
			this->modif_queue_mtx.unlock();
			logger->lock_info(fmt::format("manualStartModification : Released modif_queue lock for OrderID: {}", exec_ord_num));
			logger->ord_info(fmt::format(
				"manualStartModification : Sent manual modification for id {} and pushed to modif_queue having current size: {}.", exec_ord_num, mqs));
		}
		else
		{
			logger->ord_info(
				fmt::format("COrderManager : Order id {} has been cancelled. "
							"Skipping manual modification",
							exec_ord_num));
		}
	}
	else
	{
		logger->ord_error(
			fmt::format("COrderManager : Order id {} not found in ord hist. "
						"Skipping manual modification",
						exec_ord_num));
	}
	this->ord_hist_mtx.unlock();
	logger->lock_info(fmt::format("manualStartModification : Released ord_hist lock for OrderID: {}", exec_ord_num));
}

void COrderManager::manualStopModification(std::int32_t exec_ord_num)
{
	logger->lock_info(fmt::format("manualStopModification : Acquiring ord_hist lock for OrderID: {}", exec_ord_num));
	this->ord_hist_mtx.lock();
	if (ord_hist.contains(exec_ord_num))
	{
		auto pack_ptr = &ord_hist[exec_ord_num];
		if (!(pack_ptr->is_cancel))
		{
			pack_ptr->is_new = false;
			pack_ptr->is_modif = false;
			pack_ptr->is_cancel = false;
			pack_ptr->is_reject = true;
			pack_ptr->rej_reason = "Manually removed from modification queue";
			logger->ord_info(
				fmt::format("manualStopModification : Set OrderID: {} to be manually removed "
							"from modification queue",
							exec_ord_num));

			logger->lock_info(fmt::format("manualStopModification : Acquiring curr_exec_symbol lock for OrderID: {} and setting it to {}", exec_ord_num, 0));
			this->curr_exec_symbols_mtx.lock();
			curr_exec_symbols[pack_ptr->symbol] = 0;
			this->curr_exec_symbols_mtx.unlock();
			logger->lock_info(fmt::format("manualStopModification : Released curr_exec_symbol lock for OrderID: {} and setting it to {}", exec_ord_num, 0));

			data_mngr->updateOrderInfo(*pack_ptr);
		}
	}
	else
	{
		logger->ord_error(
			fmt::format("manualStopModification : OrderID: {} not found in ord hist. "
						"Skipping manual removal from modif_queue",
						exec_ord_num));
	}
	this->ord_hist_mtx.unlock();
	logger->lock_info(fmt::format("manualStopModification : Released ord_hist lock for OrderID: {}", exec_ord_num));
}
