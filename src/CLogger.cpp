/**
 * @file CLogger.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2021-08-03
 *
 * @copyright Copyright (c) 2021
 *
 */

#ifndef CLOGGER_CPP
#define CLOGGER_CPP

#include <ctime>
#include <iostream>
#include <chrono>
#include <spdlog/async.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/spdlog.h>
#include "TradingLog.pb.h"
#include "cppkafka/cppkafka.h"
#include "CUtility.hpp"
#define KAFKA_LOG_DAY_FORMAT "%m-%d-%Y"
#define KAFKA_LOG_TIME_FORMAT "%H:%M:%S"

namespace CUtility
{
	std::string getConfig(std::string section, std::string var_name, std::string default_val);
	std::string getCurrDay(std::string s_format);
	std::string getCurrTimeStampStr(std::string s_format);
};
class CLogger
{
	std::shared_ptr<spdlog::logger> sys_logger;
	std::shared_ptr<spdlog::logger> ord_logger;
	std::shared_ptr<spdlog::logger> err_logger;
	std::shared_ptr<spdlog::logger> lock_logger;
	cppkafka::Producer *producer = nullptr;
	cppkafka::MessageBuilder *builder = nullptr;
	cppkafka::MessageBuilder *heartbeat_builder = nullptr;
	cppkafka::MessageBuilder *proto_builder = nullptr;
	std::int32_t kafka_partition_val;
	int proto_message_id;
	std::mutex proto_message_mtx;
	std::chrono::time_point<std::chrono::system_clock> last_flush_time;

public:
	CLogger()
	{
		time_t now = time(0);
		proto_message_id = 1;
		tm *l_time = localtime(&now);
		std::string s_date, s_month, s_year;
		s_date = std::to_string(l_time->tm_mday);
		s_date = std::string(2 - s_date.length(), '0').append(s_date);
		s_month = std::to_string((l_time->tm_mon) + 1);
		s_month = std::string(2 - s_month.length(), '0').append(s_month);
		s_year = std::to_string(1900 + l_time->tm_year);
		std::string log_file = s_year + s_month + s_date + ".log";
		spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%f %z] [%n] [%l] %v");
		sys_logger = spdlog::basic_logger_mt<spdlog::async_factory>(
			"sys_logger", "./logs/Sys_" + log_file);
		ord_logger = spdlog::basic_logger_mt<spdlog::async_factory>(
			"ord_logger", "./logs/Ord_" + log_file);
		err_logger = spdlog::basic_logger_mt<spdlog::async_factory>(
			"err_logger", "./logs/Err_" + log_file);
		lock_logger = spdlog::basic_logger_mt<spdlog::async_factory>(
			"lock_logger", "./logs/Lock_" + log_file);
		spdlog::flush_on(spdlog::level::info);
		last_flush_time = std::chrono::system_clock::now();

		std::string kafka_ip = CUtility::getConfig("Kafka", "kafka_ip", "0.0.0.0");
		std::string kafka_port = CUtility::getConfig("Kafka", "kafka_port", "9092");
		std::string kafka_topic = CUtility::getConfig("Kafka", "kafka_topic_name", "test_mcx_stream");
		std::string kafka_heartbeat_topic = CUtility::getConfig("Kafka", "kafka_hearbeat_topic_name", "kafka_heartbeat_stream");
		std::string kafka_proto_topic = CUtility::getConfig("Kafka", "kafka_proto_topic", "test_proto_mcx");

		this->kafka_partition_val = std::stoi(CUtility::getConfig("Kafka", "kafka_partition_val", "0"));
		cppkafka::Configuration kafka_config =
			{{"metadata.broker.list", kafka_ip + ":" + kafka_port}};
		try
		{
			this->producer = new cppkafka::Producer(kafka_config);
			this->builder = new cppkafka::MessageBuilder(kafka_topic);
			this->builder->partition(this->kafka_partition_val);
			this->heartbeat_builder = new cppkafka::MessageBuilder(kafka_heartbeat_topic);
			this->heartbeat_builder->partition(this->kafka_partition_val);
			this->proto_builder = new cppkafka::MessageBuilder(kafka_proto_topic);
			this->proto_builder->partition(this->kafka_partition_val);
		}
		catch (std::exception &ex)
		{
			this->sys_error(ex.what());
		}
	}

	void sys_warn(std::string msg) { sys_logger->warn(msg); }

	void ord_warn(std::string msg) { ord_logger->warn(msg); }

	void sys_info(std::string msg) { sys_logger->info(msg); }

	void ord_info(std::string msg) { ord_logger->info(msg); }

	void lock_info(std::string msg) { lock_logger->info(msg); }

	void sys_error(std::string msg) { sys_logger->error(msg); }

	void ord_error(std::string msg) { ord_logger->error(msg); }

	void sys_critical(std::string msg) { sys_logger->critical(msg); }

	void ord_critical(std::string msg) { ord_logger->critical(msg); }

	void raise_error(std::string msg) { err_logger->critical(msg); }

	void kafka_log(std::string msg)
	{
		std::string date = CUtility::getCurrDay(KAFKA_LOG_DAY_FORMAT);
		std::string time = CUtility::getCurrTimeStampStr("%Y-%m-%d %H:%M:%S").substr(11) + ".000";
		std::string log_msg = date + "," + time + "," + msg;
		this->kafka_send(log_msg);
	}

	void kafka_send(std::string log)
	{
		if (this->producer)
		{
			this->producer->produce(this->builder->partition(this->kafka_partition_val).payload(log));
			this->producer->flush();
			this->sys_info("Kafka log sent: " + log);
		}
		else
		{
			this->sys_error("Kafka producer not initialized.");
		}
	}

	void kafka_heartbeat_log(std::string heartbeat)
	{
		if (this->producer)
		{
			this->producer->produce(this->heartbeat_builder->partition(this->kafka_partition_val).payload(heartbeat));
			this->producer->flush();
			this->sys_info("Kafka heartbeat sent: " + heartbeat);
		}
		else
		{
			this->sys_error("Kafka producer not initialized.");
		}
	}

	void kafka_send_proto(ExecutionLogs::BaseLog base_log)
	{
		if (!this->producer || !this->proto_builder) {
			this->sys_error("Kafka producer or proto_builder not properly initialized");
			return;
		}
		
		proto_message_mtx.lock();
		base_log.set_message_id(this->proto_message_id);
		this->proto_message_id += 1;
		proto_message_mtx.unlock();

		try {
			std::string serialized_log = base_log.SerializeAsString();
			
			this->producer->produce(
				this->proto_builder->partition(this->kafka_partition_val).payload(serialized_log)
			);
			
			auto curr_time = std::chrono::system_clock::now();
			auto elapsed_seconds = std::chrono::duration_cast<std::chrono::seconds>(
				curr_time - last_flush_time
			).count();
			
			if(elapsed_seconds > 10) {
				this->producer->flush();
				last_flush_time = curr_time;
			}
			
			this->sys_info("Proto Kafka log sent with log_id: " + std::to_string(base_log.message_id()));
		}
		catch (const std::exception& e) {
			this->sys_error("Failed to send proto kafka log: " + std::string(e.what()));
		}
		catch (...) {
			this->sys_error("Unknown error in kafka_send_proto");
		}
	}
};

#endif