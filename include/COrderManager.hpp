/**
 * @file COrderManager.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2021-08-20
 * 
 * @copyright Copyright (c) 2021
 * 
 */
#ifndef CORDERMANAGER_HPP
#define CORDERMANAGER_HPP

#include "CSender.hpp"
#include "TradingLog.pb.h"
class CSender;

class COrderManager
{
	CSender *sender;
	std::int32_t ord_counter = DEFAULT_EXEC_ORD_COUNTER;
	std::int32_t exch_ord_counter = DEFAULT_EXCH_ORD_COUNTER;

	std::mutex ord_hist_mtx;
	std::mutex order_queue_mtx;
	std::mutex modif_queue_mtx;
	std::mutex orders_to_send_queue_mtx;
	std::mutex orders_to_send_map_mtx;
	std::mutex curr_exec_symbols_mtx;
	std::mutex heartbeat_queue_mtx;
	std::mutex sent_order_time_mtx;

	std::unordered_map<std::string, int> curr_exec_symbols;
	std::unordered_map<std::string, std::list<OrderRequest> > orders_to_send_map;

	std::queue<OrderRequest> order_queue;
	std::queue<OrderInfo> orders_to_send_queue;
	std::queue<std::int32_t> modif_queue;

	std::vector<std::string> market_snap_log;
	
	std::unordered_map<std::string, uint32_t> sent_order_time;
	// std::unordered_map<std::int32_t, std::int32_t> cancel_and_resent_orders;

public:
	std::unordered_map<uint32_t, OrderInfo> ord_hist;
	COrderManager(CSender *_sender);
	void pushOrderRequest(OrderRequest order);
	int setExecOrdNum(int new_exec_ord_num);
	OrderRequest popOrderRequest();

	/**
	 * @brief Perform netoff of the split order present in orders_to_send_map
	 * and the current order to be placed. Subsequently, generate two Trd 
	 * responses if netoff occurs
	 * 
	 * @param split_order Order present in he orders_to_send_map
	 * @param curr_order Order to be added to orders_to_send_map
	 */
	void netoffOrders(OrderRequest &split_order, OrderRequest &curr_order);

	/**
	 * @brief Pick the symbol, go to the list of split orders for the 
	 * curr_symbol present in orders_to_send_map and netoff the split 
	 * orders
	 * 
	 * @param curr_order Current order which has to be net-ed-off
	 * @return OrderRequest 
	 */
	OrderRequest netoffSymbol(OrderRequest curr_order);

	/**
	 * @brief Decide the optimal order size based on the current market snapshot
	 * Logic will vary based on the liquidity of the symbol and the order price should not
	 * exceed the maximum transaction price limit set by the exchange
	 *  
	 * @param symbol 
	 * @param direction 
	 * @return std::uint32_t 
	 */
	std::uint32_t getCurrOrderSize(std::string symbol, std::uint32_t direction);

	// Split the order and add to the orders_to_send_map
	void addOrderToSendMap(OrderRequest curr_order);

	OrderInfo getOrderFromSendMap(OrderRequest *curr_order);

	/**
	 * @brief Dequeue orders received from CReceiver and perform netoff
	 * and order splitting operations
	 * 
	 */
	void processEntryRequests();

	
	/**
	 * @brief Pop symbolwise orders from orders_to_send_map and send to 
	 * CSender
	 * 
	 */
	void sendNewOrders();
	void processPendingRequests();

	// Get ord and trd info and update order_map
	void processOrdResponses(const aurora::OrderPacket &order_packet);
	void processTrdResponses(const aurora::TradePacket &trade_packet);
	void repopulatePendingOrders();

	void updateMarketSnapForLogging(const std::string symbol);
	std::string getAvgTrdPriceForLog();
	std::string getTotalBuyQtyForLog();
	std::string getTotalSellQtyForLog();

	void updateSentOrderTime(std::string exec_ord_num, bool insert);
	std::vector<std::string> getTimedOutOrders();
	void checkForTimedOutOrders();
	void handleTimedOutOrders(std::vector<std::string> timed_out_orders);
	void clearCurrExecSymbol(std::string symbol);
	void manualCancellation(std::int32_t exec_ord_num);
	void manualStartModification(std::int32_t exec_ord_num, std::int32_t ord_px, std::uint32_t ord_qty);
	void manualStopModification(std::int32_t exec_ord_num);
};
#endif