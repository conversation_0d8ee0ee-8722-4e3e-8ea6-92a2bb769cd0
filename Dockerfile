FROM ubuntu:20.04
ENV TZ="Asia/Kolkata"
RUN date

COPY . /usr/src/mcx-execution-system

RUN apt-get update && DEBIAN_FRONTEND="noninteractive" apt-get -y install cmake git vim gdb gcc-9 g++-9 build-essential python3 autoconf automake libtool curl make unzip libprotobuf-dev
RUN apt install -y librdkafka-dev libboost-all-dev
RUN mkdir -p /usr/src/Downloads

WORKDIR /usr/src/Downloads/
RUN git clone https://github.com/redis/hiredis.git
WORKDIR /usr/src/Downloads/hiredis/
RUN make -j 4
RUN make install

WORKDIR /usr/src/Downloads/
RUN git clone https://github.com/sewenew/redis-plus-plus.git
RUN mkdir /usr/src/Downloads/redis-plus-plus/build/
WORKDIR /usr/src/Downloads/redis-plus-plus/build
RUN cmake -DREDIS_PLUS_PLUS_CXX_STANDARD=2a ..
RUN make -j 4
RUN make install

WORKDIR /usr/src/Downloads/
RUN git clone -b 7.6 https://github.com/jtv/libpqxx.git
RUN apt install -y libpq-dev postgresql-server-dev-all
RUN mkdir -p /usr/src/Downloads/libpqxx/build
WORKDIR /usr/src/Downloads/libpqxx/build
RUN cmake -DSKIP_BUILD_TEST=on ..
RUN make -j 4
RUN make install

WORKDIR /usr/src/Downloads/
RUN git clone --recurse-submodules -b v1.38.0 https://github.com/grpc/grpc
RUN apt-get install -y build-essential autoconf libtool pkg-config
RUN mkdir -p /usr/src/mcx-execution-system/libs/grpc
RUN mkdir -p /usr/src/Downloads/grpc/cmake/build
WORKDIR /usr/src/Downloads/grpc/cmake/build
RUN export GRPC_INSTALL_DIR=/usr/src/mcx-execution-system/libs/grpc
RUN cmake -DgRPC_INSTALL=ON -DgRPC_BUILD_TESTS=OFF -DCMAKE_INSTALL_PREFIX=$GRPC_INSTALL_DIR ../..
RUN make -j 4
RUN make install
RUN mkdir -p /usr/src/Downloads/grpc/third_party/abseil-cpp/cmake/build
WORKDIR /usr/src/Downloads/grpc/third_party/abseil-cpp/cmake/build
RUN cmake -DCMAKE_INSTALL_PREFIX=$GRPC_INSTALL_DIR -DCMAKE_POSITION_INDEPENDENT_CODE=TRUE ../..
RUN make -j 4
RUN make install

WORKDIR /usr/src/Downloads/
RUN git clone https://github.com/mfontanini/cppkafka.git
WORKDIR /usr/src/Downloads/cppkafka/
RUN mkdir -p /usr/src/Downloads/cppkafka/build/
WORKDIR /usr/src/Downloads/cppkafka/build
RUN cmake ..
RUN make -j 4
RUN make install

WORKDIR /usr/src/Downloads/
RUN curl -OL https://github.com/protocolbuffers/protobuf/releases/download/v3.16.0/protobuf-all-3.16.0.tar.gz
RUN tar -zxvf protobuf-all-3.16.0.tar.gz
WORKDIR /usr/src/Downloads/protobuf-3.16.0/
RUN ./configure
RUN make -j16
RUN make install
RUN ldconfig

WORKDIR /usr/src/mcx-execution-system/
RUN rm -rf build
RUN mkdir build
WORKDIR /usr/src/mcx-execution-system/build
RUN cmake ..
RUN make -j 4

RUN apt install -y python3-pip
RUN pip install grpcio grpcio-tools
RUN pip install minio
RUN pip install pymsteams