import pymsteams
import time
import datetime
from kafka import KafkaProducer

ctclTeamsWebHookAlerts = pymsteams.connectorcard(
    "https://kivicapitalin.webhook.office.com/webhookb2/014ae612-4850-400b-bb12-4c9b13853872@b5b1a78b-9721-421b-a3e5-b56d1f6ea9e2/IncomingWebhook/ffd0fe2d322442809f7ca768d0e1a203/e0519c7b-336c-4151-bfad-c455642569e0/V2qx5pq4AhKLjU6exbojaWCfGRaF5oyMCy1HSs4opkvGs1",
    verify=False,
)
ctclTeamsWebHook = pymsteams.connectorcard(
    "https://kivicapitalin.webhook.office.com/webhookb2/014ae612-4850-400b-bb12-4c9b13853872@b5b1a78b-9721-421b-a3e5-b56d1f6ea9e2/IncomingWebhook/d8fa7f1a690a46ab9bc854a05e1a827e/e0519c7b-336c-4151-bfad-c455642569e0/V2WhpRVkAu37DU8aXr6Hz-jGTW_9F3jnhhpT9LHqAsJYo1",
    verify=False,
)
pendingOrderExecAlerts = pymsteams.connectorcard(
    "https://kivicapitalin.webhook.office.com/webhookb2/014ae612-4850-400b-bb12-4c9b13853872@b5b1a78b-9721-421b-a3e5-b56d1f6ea9e2/IncomingWebhook/4d58fe446bad4e9297fefbc6a1066555/e0519c7b-336c-4151-bfad-c455642569e0/V2sSQzMvIIol83Icgr1b4KH1c55d8FklXRpDLLfFSZNZk1",
    verify=False,
)

kafka_topic = "kafka_heartbeat_stream"
kafka_producer = KafkaProducer(bootstrap_servers = ['192.168.0.198:9092'])

def push_to_teams(line):
    ctclTeamsWebHook.title("MCX CTCL system alerts")
    ctclTeamsWebHook.text("".join(line))
    try:
        ctclTeamsWebHook.send()
    except:
        print('temp')
def is_new_line(line, last_read_ts):
    # Returns (is_new_line, last_read_ts)
    try:
        logtime = line.split("]")[0][1:]
        logtime = datetime.datetime.strptime(logtime, "%Y-%m-%d %H:%M:%S.%f %z")
        if (logtime.time() > last_read_ts.time()):
            return (True, logtime)
        else:
            return (False, last_read_ts)
    except:
        return (False, last_read_ts)

def isCurr(line):
    global latest_log_ts
    try:
        logtime = line.split("]")[0][1:]
        logtime = datetime.datetime.strptime(logtime, "%Y-%m-%d %H:%M:%S.%f %z")
        if logtime.time() > latest_log_ts.time():
            latest_log_ts = logtime
            return True
        return False
    except:
        return False

# [2024-08-12 09:10:01.541376 +05:30] [ord_logger] [info] Received entry signal from BaLTE : (10010275452, CRUDEOIL14-Aug-2024CE650000, 1, 3 lots, cluster_jordan_com strategy, airavat_jordan_oil slave)
def isOrder(line):
    if "received entry signal from balte" in line.lower():
        return True
    return False


# [2024-08-12 09:11:13.971895 +05:30] [ord_logger] [info] COrderManager : Trade notif received for balte id 10010275454, 2, 100001, CRUDEOIL14-Aug-2024CE640000, 12380, 1, 1 lots
def isTrade(line):
    if "trade notif received for" in line.lower():
        return True
    return False

# [2024-08-12 09:11:11.932301 +05:30] [ord_logger] [info] COrderManager : Received order rejection for id 100000 with msg Order 100000 has been executed completely
def isReject(line):
    if "received order rejection" in line.lower():
        return True
    return False


curr_orders = {}
curr_trades = {}
id_to_symbol = {}
pending_orders = {}

start_date = datetime.datetime.now()
latest_log_ts = datetime.datetime.now()
last_read_err = datetime.datetime.now()
last_read_sys = datetime.datetime.now()
prev_line = ""

while start_date.time() < datetime.time(8, 30):
    time.sleep(30)
    start_date = datetime.datetime.now()

end_date = datetime.datetime.combine(
    datetime.datetime.today().date(), datetime.time(23, 55)
)
# end_date = start_date + datetime.timedelta(hours=14, minutes=55)
path = (
    f"/usr/src/mcx-execution-system/build/logs/Ord_{start_date.strftime('%Y%m%d')}.log"
)
path_sys = (
    f"/usr/src/mcx-execution-system/build/logs/Sys_{start_date.strftime('%Y%m%d')}.log"
)
path_err = (
    f"/usr/src/mcx-execution-system/build/logs/Err_{start_date.strftime('%Y%m%d')}.log"
)
#https://kivicapitalin.webhook.office.com/webhookb2/014ae612-4850-400b-bb12-4c9b13853872@b5b1a78b-9721-421b-a3e5-b56d1f6ea9e2/IncomingWebhook/13b68c9573e844059ee348000968a2e3/b7418caa-bc46-4b83-a9e5-43fc9078e5cd
# path = f"/usr/src/mcx-execution-system/build/logs/Ord_20210924.log"
# start_date = datetime.datetime(2021, 9, 24, 18, 0)
# latest_log_ts = datetime.datetime(2021, 9, 24, 18, 0)
# end_date = datetime.datetime(2021, 9, 24, 23, 55)
while start_date <= end_date:
    kafka_producer.send(kafka_topic, "alerts_heartbeat".encode())
    balte_recv = []
    ord_rejections = []
    ord_rejections_issues = []
    trd_confs = set()
    with open(path_err, "r") as err_file:
        for line in err_file:
            isNew, latest_ts = is_new_line(line, last_read_err)
            if isNew:
                last_read_err = latest_ts
                if "teams" in line.lower():
                    push_to_teams(line)
    
    with open(path_sys, "r") as sys_file:
        for line in sys_file:
            isNew, latest_ts = is_new_line(line, last_read_sys)
            if isNew:
                last_read_sys = latest_ts
                if "teams" in line.lower():
                    push_to_teams(line)
        if latest_ts < datetime.datetime.now(latest_ts.tzinfo) - datetime.timedelta(seconds=30):
                push_to_teams("Execution system down: Heartbeat not received in the past 30 seconds")

    with open(path, "r") as f:
        for line in f:
            prev_line = line
            if not isCurr(line):
                continue
            if isOrder(line):
                balte_recv.append(line)
                order_logtime = line.split("]")[0][1:]
                order_logtime = datetime.datetime.strptime(order_logtime, "%Y-%m-%d %H:%M:%S.%f %z")
                ord_details = line.lower().split("balte")[1]
                ord_details = ord_details.replace("lots", "")
                ord_details = ord_details.strip(" :()\n")
                ord_details = ord_details.replace(" ", "").split(",")
                curr_orders[ord_details[0]] = int(ord_details[-3])
                curr_trades[ord_details[0]] = 0
                id_to_symbol[ord_details[0]] = ord_details[1].upper()
                pending_orders[ord_details[0]] = order_logtime

            # [2024-08-12 09:11:11.932301 +05:30] [ord_logger] [info] COrderManager : Received order rejection for id 100000 with msg Order 100000 has been executed completely
            if isReject(line):
                if "executed completely" in line:
                    ord_rejections.append(line)
                else:
                    ord_rejections_issues.append(line)
            # [2024-08-12 09:11:13.971895 +05:30] [ord_logger] [info] COrderManager : Trade notif received for balte id 10010275454, 2, 100001, CRUDEOIL14-Aug-2024CE640000, 12380, 1, 1 lots
            elif isTrade(line):
                trd_details = line.lower().split("balte id")[1]
                trd_details = trd_details.replace("lots", "")
                trd_details = trd_details.strip(" :()\n")
                trd_details = trd_details.replace(" ", "").split(",")
                curr_trades[trd_details[0]] += int(trd_details[-1])
                trd_confs.add(trd_details[0])
                if curr_orders[trd_details[0]] == curr_trades[trd_details[0]]:
                    del pending_orders[trd_details[0]]
        if len(prev_line) > 0:
            latest_log_ts = prev_line.split("]")[0][1:]
            latest_log_ts = datetime.datetime.strptime(
                latest_log_ts, "%Y-%m-%d %H:%M:%S.%f %z"
            )
            
    if len(pending_orders) > 0:
        orders_pending_for_more_than_30_mins_info = []
        for id, time_received in pending_orders.items():
            if time_received < datetime.datetime.now(time_received.tzinfo) - datetime.timedelta(minutes=30):
                orders_pending_for_more_than_30_mins_info.append(f"{id}: {curr_trades[id]}/{curr_orders[id]} placed at {time_received}")
                
        if len(orders_pending_for_more_than_30_mins_info) > 0:
            pendingOrderExecAlerts.title("MCX Execution system alerts - Orders pending for more than 30 mins")
            pendingOrderExecAlerts.text("".join(orders_pending_for_more_than_30_mins_info))
            try:
                pendingOrderExecAlerts.send()
            except Exception as exception:
                print(f"Failed to send pending orders due to: {exception}")

    if len(balte_recv) > 0:
        ctclTeamsWebHook.title("MCX Execution system alerts")
        ctclTeamsWebHook.text("\n\n".join(balte_recv))
        # print(start_date, end=":")
        # print("\n".join(balte_recv))
        try:
            ctclTeamsWebHook.send()
        except:
            print("temp")
    if len(ord_rejections) > 0:
        ctclTeamsWebHook.title("MCX Execution system alerts")
        ctclTeamsWebHook.text("\n\n".join(ord_rejections))
        # print(start_date, end=":")
        # print("\n".join(ord_rejections))
        try:
            ctclTeamsWebHook.send()
        except:
            print("temp")
            
    if len(ord_rejections_issues) > 0:
        ctclTeamsWebHookAlerts.title("MCX Execution system alerts")
        ctclTeamsWebHookAlerts.text("\n\n".join(ord_rejections_issues))

        try:
            ctclTeamsWebHookAlerts.send()
        except:
            print("temp")

    if len(trd_confs) > 0:
        ctclTeamsWebHook.title("MCX Execution system alerts")
        trd_notifs = []
        for id in trd_confs:
            trd_notifs.append(
                f"BaLTE Signal {id} {id_to_symbol[id]} traded {curr_trades[id]}/{curr_orders[id]} lots"
            )
        if len(trd_notifs) > 0:
            ctclTeamsWebHook.text("\n\n".join(trd_notifs))
            # print(start_date, end=":")
            # print("\n".join(trd_notifs))
            try:
                ctclTeamsWebHook.send()
            except:
                print("temp")
        # pending_notifs = []
        # for id in curr_orders:
        #     if curr_orders[id] > curr_trades[id]:
        #         pending_notifs.append(
        #             f"BaLTE Signal {id} {id_to_symbol[id]} is pending {curr_trades[id]}/{curr_orders[id]} lots"
        #         )
        # if len(pending_notifs) > 0:
        #     ctclTeamsWebHook.title("MCX CTCL system alerts")
        #     ctclTeamsWebHook.text("\n".join(pending_notifs))
        #     # print(start_date, end=":")
        #     # print("\n".join(pending_notifs))
        #     ctclTeamsWebHook.send()
    time.sleep(30)
    # start_date = start_date + datetime.timedelta(minutes=1)
    start_date = datetime.datetime.now()
