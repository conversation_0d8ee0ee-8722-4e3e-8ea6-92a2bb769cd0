/**
 * @file CGlobals.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2021-08-03
 *
 * @copyright Copyright (c) 2021
 *
 */
#ifndef CGLOBALS_HPP
#define CGLOBALS_HPP

#include "structures.h"
#include "CDataManager.hpp"
#include "CLogger.cpp"
#include <future>
#include <iostream>
#include <mutex>
#include <queue>
#include <unordered_map>

typedef void (*THREADFUNCPTR)(void *);

#define prompt(x) std::cout << x << std::endl;
#define error_prompt(x) std::cerr << x << std::endl;
#define debug_msg(x) std::cerr << #x << " = " << x << std::endl;

#define ENT_SENT 100
#define ENT_CONF 101
#define ENT_REJC 102
#define MOD_SENT 200
#define MOD_CONF 201
#define MOD_REJC 202
#define CAN_SENT 300
#define CAN_CONF 301
#define CAN_REJC 302
#define NOT_FOUND_ 404

#define NUM_GRPC_RETRIES 5
#define GRPC_RETRY_DELAY 10

#define CONVERT_BOOL_TO_STR(x) (x) ? "true" : "false"
#define CONVERT_STR_TO_BOOL(x) (x == "t") ? true : false

#define NETOFF_KIVI_ID 65536
#define NETOFF_EXCH_ORD_ID 65536
#define NETOFF_EXCH_TRD_ID 65536

class CLogger;
inline CLogger *logger;
class CDataManager;
inline CDataManager *data_mngr;
class CReader;
inline CReader *data_rdr;

inline const int modification_timer = 10;
inline int modif_delay_lw_lmt;
inline int modif_delay_up_lmt;
inline const int order_refresh_timer = 2;
inline const int trade_refresh_timer = 2;

// TODO : Manage these futures
inline std::future<void> __send_requests__, __send_modifications__,
    __get_order_info__, __get_trade_info__, __process_entries__, __process_pending__;
// Value coeff is lot_size*(price_num/price_denum)*(general_num/general_denum)--Parsed from MCXSCRIPS.csv
inline std::unordered_map<std::string, float> value_coeff;
inline std::unordered_map<std::string, int> price_tick;
#endif