/**
 * @file structures.h
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2021-08-20
 *
 * @copyright Copyright (c) 2021
 *
 */
#ifndef STRUCTURES_CPP
#define STRUCTURES_CPP
#include <iostream>
#include <mutex>

typedef struct OrderRequest
{
	std::string symbol;
	std::string ord_type;
	std::int16_t direction;
	std::int32_t ord_px;
	std::int32_t ord_qty;
	std::string balte_id = "";
	std::uint32_t total_ord_qty = 0;
	OrderRequest()
		: symbol(""), ord_type(""), direction(0), ord_px(0), ord_qty(0),
		  balte_id("") {}
	OrderRequest(const std::string &_symbol, const std::string &_ord_type,
				 std::int16_t _direction, std::int32_t _ord_px,
				 std::int32_t _ord_qty, std::string _balte_id)
		: symbol(_symbol), ord_type(_ord_type), balte_id(_balte_id)
	{
		direction = _direction;
		ord_px = _ord_px;
		ord_qty = _ord_qty;
		total_ord_qty = _ord_qty;
	}
} OrderRequest;

typedef struct OrderInfo
{
	std::string symbol;
	std::string ord_type;
	std::int16_t direction;
	std::int32_t ord_px;
	std::int32_t trig_px;
	std::int32_t ord_qty;
	std::int32_t disc_qty;
	std::int32_t exec_ord_num;
	std::int64_t exch_trd_num = 0;
	std::uint32_t ord_conf_ts;
	bool is_new;
	bool is_modif;
	bool is_cancel;
	bool is_reject;
	std::string rej_reason;
	std::uint32_t send_ts;
	bool is_trd;
	std::int32_t trd_px;
	std::int32_t trd_qty;
	std::uint32_t trd_conf_ts;
	// mutable std::mutex ord_mtx;
	std::string balte_id;

	OrderInfo()
		: symbol(""), ord_type(""), direction(0), ord_px(0), trig_px(0),
		  ord_qty(0), disc_qty(0), exec_ord_num(0), ord_conf_ts(0), is_new(false),
		  is_modif(false), is_cancel(false), is_reject(false), rej_reason(""),
		  send_ts(0), is_trd(false), trd_px(0), trd_qty(0), trd_conf_ts(0)
	{
	}
	OrderInfo(const std::string &_symbol, const std::string &_ord_type,
			  std::int16_t _direction, std::int32_t _ord_px,
			  std::int32_t _trig_px, std::int32_t _ord_qty,
			  std::int32_t _disc_qty, std::int32_t _exec_ord_num,
			  std::uint32_t _ord_conf_ts, bool _is_new, bool _is_modif,
			  bool _is_cancel, bool _is_reject, const std::string &_rej_reason,
			  std::uint32_t _send_ts, bool _is_trd, std::int32_t _trd_px,
			  std::int32_t _trd_qty, std::uint32_t _trd_conf_ts,
			  std::string _balte_id)
		: symbol(_symbol), ord_type(_ord_type), rej_reason(_rej_reason),
		  balte_id(_balte_id)
	{
		direction = _direction;
		ord_px = _ord_px;
		trig_px = _trig_px;
		ord_qty = _ord_qty;
		disc_qty = _disc_qty;
		exec_ord_num = _exec_ord_num;
		ord_conf_ts = _ord_conf_ts;
		is_new = _is_new;
		is_modif = _is_modif;
		is_cancel = _is_cancel;
		is_reject = _is_reject;
		send_ts = _send_ts;
		is_trd = _is_trd;
		trd_px = _trd_px;
		trd_qty = _trd_qty;
		trd_conf_ts = _trd_conf_ts;
	}
} OrderInfo;

typedef struct Contract
{
	std::string symbol = "";
	std::string expiry = "";
	std::string option_type = "";
	std::string strike = "";
} Contract;

#endif