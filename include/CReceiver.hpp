/**
 * @file CReceiver.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2021-08-03
 * 
 * @copyright Copyright (c) 2021
 * 
 */
#ifndef CRECEIVER_HPP
#define CRECEIVER_HPP

#include "receiver.grpc.pb.h"
#include <grpcpp/ext/proto_server_reflection_plugin.h>
#include <grpcpp/grpcpp.h>
#include <grpcpp/health_check_service_interface.h>
#include "TradingLog.pb.h"
#include "CUtility.hpp"
#include "COrderManager.hpp"

using grpc::Server;
using grpc::ServerBuilder;
using grpc::ServerContext;

using recv_sys::RecvSignalHandler;

class CReceiver final : public RecvSignalHandler::Service
{
	COrderManager *ord_mngr;
	CSender *sender;
	std::unique_ptr<Server> server;
	std::vector<std::string> market_snap_log;

public:
	CReceiver(COrderManager *_ord_mngr, CSender *_sender);
	grpc::Status sendOrder(ServerContext *context, const recv_sys::OrderEntryRequest *req, recv_sys::Ack *ack);
	grpc::Status updateIds(ServerContext *context, const recv_sys::IdUpdateRequest *req, recv_sys::Ack *ack);
	grpc::Status clearCurrExecSymbol(ServerContext *context, const recv_sys::CurrExecSymbol *req, recv_sys::Ack *ack);
	grpc::Status manualCancellation(ServerContext *context, const recv_sys::ExecOrderNumList *req, recv_sys::Ack *ack);
	grpc::Status manualStartModification(ServerContext *context, const recv_sys::ModifRequest *req, recv_sys::Ack *ack);
	grpc::Status manualStopModification(ServerContext *context, const recv_sys::ExecOrderNumList *req, recv_sys::Ack *ack);
	void updateMarketSnapForLogging(const std::string symbol);
	std::string getTotalBuyQtyForLog();
	std::string getTotalSellQtyForLog();
	void serve(void *v);
	void stopServer();
};

#endif
