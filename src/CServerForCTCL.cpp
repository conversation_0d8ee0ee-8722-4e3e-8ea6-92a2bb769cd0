/**
 * @file CServerForCTCL.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2021-08-03
 *
 * @copyright Copyright (c) 2021
 *
 */
#include "CServerForCTCL.hpp"

CServerForCTCL::CServerForCTCL()
{
    logger->sys_info("CServerForCTCL : server initialized");
}

/**
 * @brief
 * @param
 * @returns
 */ 
void CServerForCTCL::serve(void *v)
{
    std::string grpc_addr = CUtility::getConfig("gRPC", "ctcl_to_exec_grpc_ip", "") +
                        ":" +
                        CUtility::getConfig("gRPC", "ctcl_to_exec_grpc_port", "");
	grpc::EnableDefaultHealthCheckService(true);
	grpc::reflection::InitProtoReflectionServerBuilderPlugin();
	ServerBuilder builder;
	builder.AddListeningPort(grpc_addr, grpc::InsecureServerCredentials());
	builder.RegisterService(this);
	server = builder.BuildAndStart();
	logger->sys_info(fmt::format(
		"CServerForCTCL : Receiver gRPC server listening on {}", grpc_addr));
	server->Wait();
}

/**
 * @brief
 * @param
 * @returns
 */ 
void CServerForCTCL::stopServer()
{
	if (server)
	{
		server->Shutdown();
		logger->sys_info("CServerForCTCL : Shutting down Receiver gRPC server");
	}
	else
	{
		logger->sys_info("CServerForCTCL : Receiver gRPC server not running");
	}
}

void CServerForCTCL::setOrdManager(COrderManager *_ord_mngr)
{
	this->ord_mngr = _ord_mngr;
}

grpc::Status CServerForCTCL::sendOrdInfo(ServerContext *context, const aurora::OrderPacket *req, aurora::Ack *ack)
{
	logger->sys_info(
		fmt::format(
			"Received Order Info from CTCL for : {0}",
			req->exec_ord_num()
		)
	);
	try 
	{
		this->ord_mngr->processOrdResponses(*req);		
	} 
	catch (const std::exception &e) {
		
		logger->lock_info("sendOrdInfo : Acquiring lock on heartbeat_queue_mtx");
		logger->kafka_heartbeat_log("processOrdResponses:exception");
		logger->lock_info("sendOrdInfo : released lock on heartbeat_queue_mtx");
		
		return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, e.what());
	}
	return grpc::Status::OK;
	
}

grpc::Status CServerForCTCL::sendTrdInfo(ServerContext *context, const aurora::TradePacket *req, aurora::Ack *ack)
{
	logger->sys_info(
		fmt::format(
			"Received Trade Info from CTCL for : {0}",
			req->exec_ord_num()
		)
	);
	try 
	{
		this->ord_mngr->processTrdResponses(*req);		
	} 
	catch (const std::exception &e) {
		
		logger->lock_info("sendTrdInfo : Acquiring lock on heartbeat_queue_mtx");
		logger->kafka_heartbeat_log("processTrdResponses:exception");
		logger->lock_info("sendTrdInfo : released lock on heartbeat_queue_mtx");
		
		return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, e.what());
	}
	
	return grpc::Status::OK;
}
