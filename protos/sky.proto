syntax = "proto3";
package aurora;

message LoginRequest
{
    int32 member_id = 1;
    int32 logon_id = 2;
    string password = 3;
}

message LoginResponse
{
    bool status = 1;
    string message = 2;
}

message PasswdChangeRequest
{
    int32 member_id = 1;
    int32 logon_id = 2;
    string old_password = 3;
    string new_password = 4;
}

message PasswdChangeResponse
{
    bool status = 1;
    string message = 2;
}

message LogoffRequest
{
}

message LogoffResponse
{
	string response = 1;
}

message DropCopyRequest
{
}

message OrderEntryRequest
{
    string symbol = 1;
    string order_type = 2;
    int32 order_price = 3;
    int32 trigger_price = 4;
    int32 direction = 5;
    int32 disc_quantity = 6;
    int32 ord_quantity = 7;
    string gtd = 8;
    string exec_ord_num = 9;
}

message OrderModifRequest
{
    string exec_ord_num = 1;
    int32 modif_disc_qty = 2;
    int32 modif_qty = 3;
    int32 modif_ord_px = 4;
    int32 modif_trig_px = 5;
    string modif_gtd = 6;
}

message OrderCancelRequest
{
    repeated string exec_ord_num = 1;
}

message OrderInfo
{
    string exec_ord_num = 1;
}

message ContractInfo
{
    string symbol = 1;
    int32 direction = 2;
    int32 sqoff_qty = 3;
    string exec_ord_num = 4;
}

message OrderStatus
{
    string exec_ord_num = 1;
    string order_status = 2;
    int32 kivi_ord_num = 3;
    uint64 exch_ord_num = 4;
    uint32 trd_num = 5;
    string rej_reason = 6;
}

message OrderPacket
{
    string exec_ord_num = 1;
    int32 kivi_ord_num = 2;
    uint64 exch_ord_num = 3;
    string symbol = 4;
    int32 order_price = 5;
    int32 direction = 6;
    int32 ord_qty = 7;
    int32 order_ts = 8;
    string order_type = 9;
    int32 status = 10;
    string rej_reason = 14;
    int32 trd_qty = 15;
}

message TradePacket
{
    string exec_ord_num = 1;
    int32 kivi_ord_num = 2;
    uint64 exch_ord_num = 3;
    uint32 trd_num = 4;
    string symbol = 5;
    int32 trade_price = 6;
    int32 direction = 7;
    int32 quantity = 8;
    int32 trade_ts = 9;
}

message TradeList
{
    repeated TradePacket trade_list = 1;
}

message PendingOrders
{
    map <int32, OrderPacket> pending_orders = 1;
}

message Ack
{
    int32 status = 1;
}

message RMSParameters
{
    int32 daily_exposure_limit = 1;
}

message Empty
{
}

message LoginStatus
{
    bool login_status = 1;
}

message updateKiviOrdNumRequest
{
    int32 new_kivi_ord_num = 1;
}

service SignalHandler
{
    rpc loginRequest(LoginRequest) returns (LoginResponse) {}
    rpc logoffRequest(LogoffRequest) returns (LogoffResponse) {}
    rpc passwdChangeRequest(PasswdChangeRequest) returns (PasswdChangeResponse) {}
    rpc sendOrder(OrderEntryRequest) returns (Ack) {}
    rpc sendModification(OrderModifRequest) returns (Ack) {}
    rpc sendCancellation(OrderCancelRequest) returns (Ack) {}
    rpc getOrdStatus(OrderInfo) returns (OrderPacket) {}
    rpc getTrdStatus(OrderInfo) returns (TradeList) {}
    rpc getPendingOrders(Empty) returns (PendingOrders) {}
    rpc squareOffPosition(ContractInfo) returns (Ack) {}
    rpc cancelPendingOrders(Empty) returns (Ack) {}
    rpc setRMSParameters(RMSParameters) returns (Empty) {}
    rpc dropCopy(DropCopyRequest) returns (Ack) {}
    rpc getLoginStatus(Empty) returns (LoginStatus) {}
    rpc updateKiviOrdNum(updateKiviOrdNumRequest) returns (Ack) {}
}

service CTCLToExecHandler
{
    rpc sendOrdInfo(OrderPacket) returns (Ack) {}
    rpc sendTrdInfo(TradePacket) returns (Ack) {}
}