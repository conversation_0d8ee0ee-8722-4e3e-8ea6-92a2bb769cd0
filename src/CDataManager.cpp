/**
 * @file CDataManager.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief Handle database operations
 * @version 0.0.1
 * @date 2021-06-25
 *
 * @copyright Copyright (c) 2021
 *
 */

#include "CDataManager.hpp"

/**
 * @brief Construct a new CDataManager::CDataManager object
 *
 */
CDataManager::CDataManager()
{
	std::string db_user, db_password, db_ip, db_port;
	db_name = CUtility::getConfig("Database", "db_name", "");
	db_ord_table = CUtility::getConfig("Database", "db_ord_table", "");
	db_trd_table = CUtility::getConfig("Database", "db_trd_table", "");
	db_rms_params_table =
		CUtility::getConfig("Database", "db_rms_params_table", "");
	db_meta_table = CUtility::getConfig("Database", "db_meta_table", "");
	db_balte_table = CUtility::getConfig("Database", "db_balte_table", "");

	db_user = CUtility::getConfig("Database", "db_user", "");
	db_password = CUtility::getConfig("Database", "db_password", "");
	db_ip = CUtility::getConfig("Database", "db_ip", "");
	db_port = CUtility::getConfig("Database", "db_port", "");
	try
	{
		this->conn = new pqxx::connection("dbname=" + db_name + " user=" + db_user +
										  " password=" + db_password +
										  " host=" + db_ip + " port=" + db_port);
	}
	catch (std::exception &ex)
	{
		logger->raise_error(ex.what());
	}
	if ((this->conn) && (this->conn->is_open()))
		logger->sys_info("Database connection successful");
	else
		logger->sys_error("Failed to connect database");
	try
	{
		createTable(this->db_ord_table, "orderbook");
	}
	catch (std::exception &ex)
	{
		logger->raise_error(ex.what());
	}
	try
	{
		createTable(this->db_trd_table, "tradebook");
	}
	catch (std::exception &ex)
	{
		logger->raise_error(ex.what());
	}
	try
	{
		createTable(this->db_rms_params_table, "rms_params");
	}
	catch (std::exception &ex)
	{
		logger->raise_error(ex.what());
	}
	try
	{
		createTable(this->db_meta_table, "meta_data");
	}
	catch (std::exception &ex)
	{
		logger->raise_error(ex.what());
	}
	try
	{
		createTable(this->db_balte_table, "balte_orderbook");
	}
	catch (std::exception &ex)
	{
		logger->raise_error(ex.what());
	}
}

/**
 * @brief Create a table in the database
 *
 * @param table_name std::string table name
 * @param type std::string
 */
void CDataManager::createTable(std::string table_name, std::string type)
{
	std::string s_create_table;
	if (type == "orderbook")
		s_create_table =
			fmt::format("CREATE TABLE IF NOT EXISTS {}({}, PRIMARY KEY({}));",
						table_name, ORD_SCHEMA, ORD_PRIMARY_KEY);
	else if (type == "tradebook")
		s_create_table =
			fmt::format("CREATE TABLE IF NOT EXISTS {}({}, PRIMARY KEY({}));",
						table_name, TRD_SCHEMA, TRD_PRIMARY_KEY);
	else if (type == "rms_params")
		s_create_table =
			fmt::format("CREATE TABLE IF NOT EXISTS {}({}, PRIMARY KEY({}));",
						table_name, RMS_PARAMS_SCHEMA, RMS_PARAMS_PRIMARY_KEY);
	else if (type == "meta_data")
		s_create_table = fmt::format("CREATE TABLE IF NOT EXISTS {}({});",
									 table_name, META_DATA_SCHEMA);
	else if (type == "balte_orderbook")
		s_create_table =
			fmt::format("CREATE TABLE IF NOT EXISTS {}({}, PRIMARY KEY({}));",
						table_name, BALTE_SCHEMA, BALTE_PRIMARY_KEY);
	else
	{
		logger->sys_warn("Wrong typename.Skipping table creation");
		return;
	}
	this->db_mtx.lock();
	pqxx::work W(*(this->conn));
	W.exec(s_create_table);
	W.commit();
	this->db_mtx.unlock();
}

/**
 * @brief Insert data to orderbook table
 *
 * @param table_name table name
 * @param pack `ord` packet
 */
void CDataManager::insertOrderInfo(const OrderInfo &pack)
{
	std::string entry_ts = CUtility::convertEpochToTS(pack.ord_conf_ts);
	std::string entry_day = entry_ts.substr(0, 10);
	std::string send_ts =
		CUtility::convertEpochToTS(pack.send_ts + 5 * 3600 + 1800);
	std::string s_insert_row = "";
	std::lock_guard<std::mutex> guard(this->db_mtx);
	try
	{
		s_insert_row = fmt::format(
			"INSERT INTO {0}({1}) VALUES('{2}', '{3}', {4}, {5}, {6}, {7}, {8}, "
			"{9}, '{10}', '{11}', {12}, "
			"{13}, {14}, {15}, {16}, '{17}', '{18}', {19}, '{20}');",
			this->db_ord_table, ORD_COLS, pack.symbol, pack.ord_type,
			pack.direction, pack.ord_px, pack.trig_px, pack.ord_qty, pack.disc_qty,
			pack.balte_id, pack.exec_ord_num, entry_ts, pack.is_new, pack.is_modif,
			pack.is_cancel, pack.is_reject, pack.is_trd, entry_day, send_ts,
			pack.trd_qty, pack.rej_reason);
		pqxx::work W(*(this->conn));
		W.exec(s_insert_row);
		W.commit();
	}
	catch (std::exception &ex)
	{
		logger->raise_error(fmt::format("DB insert error : {}", ex.what()));
	}
	return;
}

/**
 * @brief Insert data to balte_orderbook table
 *
 * @param table_name table name
 * @param pack `ord` packet
 */
void CDataManager::insertOrderInfo(const OrderRequest &pack)
{
	std::string entry_day = "CURRENT_DATE";
	std::string s_insert_row = "";
	this->db_mtx.lock();
	try
	{
		s_insert_row = fmt::format(
			"INSERT INTO {0}({1}) VALUES({2}, '{3}', '{4}', {5}, {6}, {7}, {8}, "
			"{9}) ON CONFLICT ({10}) DO UPDATE SET ord_qty={11};",
			this->db_balte_table, BALTE_COLS, entry_day, pack.symbol, pack.ord_type,
			pack.direction, pack.ord_px, pack.ord_qty, pack.total_ord_qty,
			pack.balte_id, BALTE_PRIMARY_KEY, pack.ord_qty);
		pqxx::work W(*(this->conn));
		W.exec(s_insert_row);
		W.commit();
	}
	catch (std::exception &ex)
	{
		logger->raise_error(fmt::format("DB insert error : {}", ex.what()));
	}
	this->db_mtx.unlock();
	return;
}

/**
 * @brief Update orderbook row based on order modification or cancellation
 * status
 *
 * @param pack order packet
 */
void CDataManager::updateOrderInfo(const OrderInfo &pack)
{
	std::string s_modifications;
	std::string send_ts =
		CUtility::convertEpochToTS(pack.send_ts + 5 * 3600 + 1800);
	if (pack.is_reject)
	{
		s_modifications = fmt::format(
			"is_new = {}, is_modif = {}, is_cancel = {}, is_reject = "
			"true, rej_reason = '{}'",
			CONVERT_BOOL_TO_STR(pack.is_new), CONVERT_BOOL_TO_STR(pack.is_modif),
			CONVERT_BOOL_TO_STR(pack.is_cancel), pack.rej_reason);
	}
	else if (pack.is_cancel)
	{
		s_modifications =
			fmt::format("is_new = false, is_modif = false, is_cancel = true");
	}
	else if (pack.is_modif)
	{
		s_modifications = fmt::format(
			"disc_qty = {}, ord_qty = {}, ord_px = {}, trig_px = {}, is_new = "
			"false, is_modif = true, send_modif_ts = '{}', trd_qty = {}",
			pack.disc_qty, pack.ord_qty, pack.ord_px, pack.trig_px, send_ts,
			pack.trd_qty);
	}
	else if (pack.is_new)
	{
		s_modifications = fmt::format(
			"disc_qty = {}, ord_qty = {}, ord_px = {}, trig_px = {}, trd_qty = {}",
			pack.disc_qty, pack.ord_qty, pack.ord_px, pack.trig_px, pack.trd_qty);
	}
	else
	{
		logger->sys_error(
			fmt::format("Check is_new, is_modif or is_cancel flag for order {}",
						pack.exec_ord_num));
		return;
	}
	std::string entry_day =
		CUtility::convertEpochToTS(pack.ord_conf_ts).substr(0, 10);
	std::string s_update_row = fmt::format(
		"UPDATE {} SET {} WHERE entry_day = '{}' AND exec_ord_num = '{}';",
		this->db_ord_table, s_modifications, entry_day, pack.exec_ord_num);
	std::lock_guard<std::mutex> guard(this->db_mtx);
	try
	{
		pqxx::work W(*(this->conn));
		W.exec(s_update_row);
		W.commit();
	}
	catch (std::exception &ex)
	{
		logger->raise_error(fmt::format("DB update error : {}", ex.what()));
	}
}

void CDataManager::updateOrderInfo(const OrderRequest &pack)
{
	std::string s_modifications;
	s_modifications = fmt::format("ord_qty = {}", pack.ord_qty);

	std::string entry_day = "CURRENT_DATE";

	std::string s_update_row = fmt::format(
		"UPDATE {} SET {} WHERE entry_day = {} AND balte_trd_num = '{}';",
		this->db_ord_table, s_modifications, entry_day, pack.balte_id);
	std::lock_guard<std::mutex> guard(this->db_mtx);
	try
	{
		pqxx::work W(*(this->conn));
		W.exec(s_update_row);
		W.commit();
	}
	catch (std::exception &ex)
	{
		logger->raise_error(fmt::format("DB update error : {}", ex.what()));
	}
}

void CDataManager::deleteOrderInfo(const OrderInfo &pack)
{
	std::string entry_ts = CUtility::convertEpochToTS(pack.ord_conf_ts);
	std::string entry_day = entry_ts.substr(0, 10);
	this->db_mtx.lock();
	try
	{
		std::string s_delete_row = fmt::format(
			"DELETE FROM {0} WHERE entry_day = '{1}' AND exec_ord_num = '{2}';",
			this->db_ord_table, entry_day, pack.exec_ord_num);
		pqxx::work W(*(this->conn));
		W.exec(s_delete_row);
		W.commit();
	}
	catch (std::exception &ex)
	{
		logger->raise_error(fmt::format("DB insert error : {}", ex.what()));
	}
	this->db_mtx.unlock();
	return;
}

/**
 * @brief Insert data into trdbook table
 *
 * @param table_name table name
 * @param pack `trd` packet
 * @param dropcopy_flag indicates if the response comes from a drop copy
 * operation
 */
void CDataManager::insertTradeInfo(const OrderInfo &pack)
{
	if (!(pack.is_trd))
	{
		logger->sys_error(fmt::format("Cannot update tradebook table for "
									  "exec_ord_num {}. `is_trd` flag is false.",
									  pack.exec_ord_num));
		return;
	}
	std::string trd_time = CUtility::convertEpochToTS(pack.trd_conf_ts);
	std::string entry_day = trd_time.substr(0, 10);
	std::string s_insert_row =
		fmt::format("INSERT INTO {0}({1}) VALUES('{2}', {3}, {4}, {5}, '{6}', "
					"'{7}', '{8}', {9}, {10});",
					this->db_trd_table, TRD_COLS, pack.symbol, pack.direction,
					pack.trd_qty, pack.trd_px, trd_time, entry_day,
					pack.exec_ord_num, pack.exch_trd_num, pack.balte_id);
	std::string s_update_row = fmt::format(
		"UPDATE {} SET trade_flag = true, trd_qty = {} WHERE entry_day = '{}' "
		"AND exec_ord_num = '{}';",
		this->db_ord_table, pack.trd_qty, entry_day, pack.exec_ord_num);
	std::lock_guard<std::mutex> guard(this->db_mtx);
	try
	{
		pqxx::work W(*(this->conn));
		W.exec(s_insert_row);
		W.exec(s_update_row);
		W.commit();
	}
	catch (std::exception &ex)
	{
		logger->raise_error(fmt::format("DB insert error : {}", ex.what()));
	}
}

/**
 * @brief Get the next order counter number from order book in db
 *
 */
int CDataManager::getOrderCounterNumber(bool is_exec)
{
	std::string s_ord_num;
	int ret_val;
	if (is_exec)
	{
		ret_val = DEFAULT_EXEC_ORD_COUNTER;
		s_ord_num = fmt::format(
			"SELECT exec_ord_counter FROM {} WHERE entry_day=CURRENT_DATE;",
			this->db_meta_table);
	}
	else
	{
		ret_val = DEFAULT_EXCH_ORD_COUNTER;
		s_ord_num = fmt::format(
			"SELECT exch_ord_counter FROM {} WHERE entry_day=CURRENT_DATE;",
			this->db_meta_table);
	}
	this->db_mtx.lock();
	try
	{
		pqxx::nontransaction N(*(this->conn));
		pqxx::result R = N.exec(s_ord_num);
		if (!(R.empty()))
			ret_val = (R[0][0].as<int>() + 1);
	}
	catch (const std::exception &ex)
	{
		logger->raise_error(fmt::format("DB read error : {}", ex.what()));
	}
	this->db_mtx.unlock();
	return ret_val;
}

std::vector<std::vector<std::string>> CDataManager::getPendingOrdersFromDB()
{
	std::vector<std::vector<std::string>> pendingOrders;
	std::vector<std::string> row;
	std::string s_pending_ords =
		fmt::format("SELECT {} FROM {} WHERE ((is_new=TRUE) or (is_modif=TRUE) "
					"or (rej_reason = 'Manually removed "
					"from modification queue')) "
					"AND entry_day = CURRENT_DATE AND (ord_qty > 0);",
					ORD_COLS, data_mngr->db_ord_table);
	this->db_mtx.lock();
	try
	{
		pendingOrders.clear();
		pqxx::nontransaction N(*(this->conn));
		pqxx::result R = N.exec(s_pending_ords);
		for (pqxx::result::const_iterator c = R.begin(); c != R.end(); ++c)
		{
			row.clear();
			for (auto field : c)
			{
				row.push_back(field.c_str());
			}
			pendingOrders.push_back(row);
		}
	}
	catch (const std::exception &ex)
	{
		logger->raise_error(fmt::format("DB read error : {}", ex.what()));
	}
	this->db_mtx.unlock();
	return pendingOrders;
}

std::vector<std::vector<std::string>> CDataManager::getBaLTEOrdersFromDB()
{
	std::vector<std::vector<std::string>> pendingOrders;
	std::vector<std::string> row;
	std::string s_pending_ords =
		fmt::format("SELECT {} FROM {} WHERE (ord_qty > 0 "
					"AND entry_day = CURRENT_DATE);",
					BALTE_COLS, data_mngr->db_balte_table);
	this->db_mtx.lock();
	try
	{
		pendingOrders.clear();
		pqxx::nontransaction N(*(this->conn));
		pqxx::result R = N.exec(s_pending_ords);
		for (pqxx::result::const_iterator c = R.begin(); c != R.end(); ++c)
		{
			row.clear();
			for (auto field : c)
			{
				row.push_back(field.c_str());
			}
			pendingOrders.push_back(row);
		}
	}
	catch (const std::exception &ex)
	{
		logger->raise_error(fmt::format("DB read error : {}", ex.what()));
	}
	this->db_mtx.unlock();
	return pendingOrders;
}

int CDataManager::updateOrderCounter(int curr_counter, bool is_exec)
{
	std::string query;
	if (is_exec)
	{
		query = fmt::format("INSERT INTO {0} (entry_day, exec_ord_counter) VALUES "
							"({1}, {2}) ON CONFLICT ({3}) "
							"DO UPDATE SET {4}={5};",
							this->db_meta_table, "CURRENT_DATE", curr_counter,
							"entry_day", "exec_ord_counter", curr_counter);
	}
	else
	{
		query = fmt::format("INSERT INTO {0} (entry_day, exch_ord_counter) VALUES "
							"({1}, {2}) ON CONFLICT ({3}) "
							"DO UPDATE SET {4}={5};",
							this->db_meta_table, "CURRENT_DATE", curr_counter,
							"entry_day", "exch_ord_counter", curr_counter);
	}
	std::lock_guard<std::mutex> guard(this->db_mtx);
	try
	{
		pqxx::work W(*(this->conn));
		W.exec(query);
		W.commit();
	}
	catch (std::exception &ex)
	{
		logger->raise_error(
			fmt::format("DB error updating order counter: {}", ex.what()));
	}
	return curr_counter + 1;
}