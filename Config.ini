﻿[Redis]
redis_ip = *************
redis_port = 6379

[Database]
db_ip = localhost
db_port = 5434
db_name = mcx_execution
db_ord_table = orderbook2_0
db_trd_table = tradebook2_0
db_balte_table = balte_orderbook
db_rms_params_table = rms_params
db_user = exec
db_password = postgres
db_meta_table = mcx_exec_meta

[gRPC]
sender_grpc_ip = *************
sender_grpc_port = 7777
ctcl_to_exec_grpc_ip = 0.0.0.0
ctcl_to_exec_grpc_port = 8778
receiver_grpc_ip = 0.0.0.0
receiver_grpc_port = 7779

[PreMarket]
scrip_path = /data/mcx_support_files/MCXScrips.csv
mcx_daylight_savings = /data/mcx_support_files/mcx_daylight_savings.csv

[Limits]
modif_delay_lw_lmt = 1
modif_delay_up_lmt = 1

[Buffer]
to_cancel_path = /usr/src/mcx-execution-system/buffer_files/to_cancel.log
to_modify_path = /usr/src/mcx-execution-system/buffer_files/to_modify.log
to_rm_modify_path = /usr/src/mcx-execution-system/buffer_files/to_rm_modify.log

[Kafka]
kafka_ip = *************
kafka_port = 9092
kafka_topic_name = live_mcx_stream
kafka_hearbeat_topic_name = kafka_heartbeat_stream
kafka_proto_topic = live_proto_mcx
kafka_partition_val = 0

[DPR]
cooldown_period = 900

[SplitSize]
max_single_transaction_value = 22000000
min_order_quantity = 2