/**
 * @file CSender.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2021-08-03
 *
 * @copyright Copyright (c) 2021
 *
 */
#include "CSender.hpp"

CSender::CSender(std::shared_ptr<Channel> channel)
	: stub_(SignalHandler::NewStub(channel))
{
	this->ord_mngr = nullptr;
	this->exchangeClosingMinute = CUtility::updateExchangeClosing();
	startThreads();
	logger->sys_info("Sender server initialized");
}

void CSender::setOrdManager(COrderManager *_ord_mngr)
{
	this->ord_mngr = _ord_mngr;
}

void CSender::startThreads()
{
	__send_requests__ = std::async(std::launch::async,
								   (THREADFUNCPTR)&CSender::sendRequests, this);
}

void CSender::pushOrderInfo(OrderInfo order)
{
	std::int32_t sqs = 0;
	logger->lock_info(fmt::format("CSender::pushOrderInfo : Acquiring send_queue_mtx lock for OrderID: {}", order.exec_ord_num));
	this->send_queue_mtx.lock();
	logger->lock_info(fmt::format("CSender::pushOrderInfo : Acquired send_queue_mtx lock for OrderID: {}", order.exec_ord_num));
	this->send_queue.push(order);
	sqs = this->send_queue.size();
	this->send_queue_mtx.unlock();
	logger->lock_info(fmt::format("CSender::pushOrderInfo : Released send_queue_mtx lock for OrderID: {}", order.exec_ord_num));
	logger->ord_info(
		fmt::format("CSender : Pushed Order {}, {} to sender_queue having current size: {}",
					order.exec_ord_num, order.ord_px, sqs));
}

OrderInfo CSender::popOrderInfo()
{
	std::int32_t sqs = 0;
	logger->lock_info("CSender : Waiting for lock pop");
	this->send_queue_mtx.lock();
	logger->lock_info("CSender : Acquiring send queue lock pop");
	OrderInfo order = this->send_queue.front();
	logger->lock_info(fmt::format("CSender : Acquired send queue lock pop for OrderID: {}", order.exec_ord_num));
	this->send_queue.pop();
	sqs = this->send_queue.size();
	this->send_queue_mtx.unlock();
	logger->lock_info(fmt::format("CSender : Released send queue lock pop for OrderID: {}", order.exec_ord_num));
	logger->ord_info(
		fmt::format("CSender : Popped Order {}, {} from sender_queue having current size: {}",
					order.exec_ord_num, order.ord_px, sqs));
	return order;
}

void CSender::sendOrder(const OrderInfo &order)
{
	aurora::OrderEntryRequest entry_req;
	entry_req.set_symbol(order.symbol);
	entry_req.set_order_type(order.ord_type);
	entry_req.set_order_price(order.ord_px);
	entry_req.set_trigger_price(order.trig_px);
	entry_req.set_direction(order.direction);
	entry_req.set_disc_quantity(order.disc_qty);
	entry_req.set_ord_quantity(order.ord_qty);
	entry_req.set_gtd("");
	entry_req.set_exec_ord_num(std::to_string(order.exec_ord_num));

	ClientContext context;
	aurora::Ack ack;
	grpc::Status status = this->stub_->sendOrder(&context, entry_req, &ack);
	if (status.ok())
	{
		logger->ord_info(
			fmt::format("CSender : Sent entry request ({}, {}, {}, {}, {}, {})",
						order.exec_ord_num, order.symbol, order.ord_type,
						order.ord_px, order.ord_qty, order.direction));
		this->ord_mngr->updateSentOrderTime(std::to_string(order.exec_ord_num),
											true);
	}
	else
	{
		logger->raise_error(
			fmt::format("[Teams Alert] sendOrder : GRPC call sendOrder failed for "
						"order {} with error message {}",
						order.exec_ord_num, status.error_message()));
	}
}

// Send only a single modification
void CSender::sendModification(const OrderInfo &order)
{
	aurora::OrderModifRequest modif_req;
	modif_req.set_exec_ord_num(std::to_string(order.exec_ord_num));
	modif_req.set_modif_disc_qty(order.disc_qty);
	modif_req.set_modif_qty(order.ord_qty);
	modif_req.set_modif_ord_px(order.ord_px);
	modif_req.set_modif_trig_px(order.trig_px);
	modif_req.set_modif_gtd("");

	ClientContext context;
	aurora::Ack ack;
	grpc::Status status =
		this->stub_->sendModification(&context, modif_req, &ack);
	if (status.ok())
	{
		logger->ord_info(fmt::format("Sent modif request ({}, {}, {}, {}, {}, {})",
									 order.symbol, order.exec_ord_num,
									 order.ord_type, order.ord_px, order.ord_qty,
									 order.direction));
		this->ord_mngr->updateSentOrderTime(std::to_string(order.exec_ord_num),
											true);
	}
	else
	{
		logger->sys_error(fmt::format(
			"[Teams Alert] sendModification GRPC call sendModification failed for "
			"order {} with order message {} and code {}",
			order.exec_ord_num, status.error_message(), status.error_code()));
	}
}

void CSender::sendCancellation(const OrderInfo &order)
{
	aurora::OrderCancelRequest cancel_req;
	cancel_req.add_exec_ord_num(std::to_string(order.exec_ord_num));
	ClientContext context;
	aurora::Ack ack;
	grpc::Status status =
		this->stub_->sendCancellation(&context, cancel_req, &ack);
	if (status.ok())
	{
		logger->ord_info(fmt::format("Sent cancellation request for order {}",
									 order.exec_ord_num));
		this->ord_mngr->updateSentOrderTime(std::to_string(order.exec_ord_num),
											true);
	}
	else
	{
		logger->sys_error(fmt::format(
			"[Teams Alert] sendCancellation : GRPC call sendCancellation failed "
			"for order {} failed with error message {}",
			order.exec_ord_num, status.error_message()));
	}
}

void CSender::sendRequests()
{
	struct tm curr_ts;
	std::int32_t sleep_timer;
	while (true)
	{
		if (this->send_queue.empty())
		{
			sleep(1);
			continue;
		}
		OrderInfo order = this->popOrderInfo();
		curr_ts = CUtility::getCurrTimeStamp();
		if ((curr_ts.tm_hour >= 23) && (curr_ts.tm_min >= (this->exchangeClosingMinute - 10)))
			sleep_timer = 0;
		else
			sleep_timer = 0;
		if (order.is_cancel)
		{
			logger->sys_info(fmt::format(
				"CSender : Sleeping for {} secs before sending cancel req",
				sleep_timer));
			sleep(sleep_timer);
			while (!this->getCTCLLoginStatus())
			{
				logger->sys_info(fmt::format(
					"[Teams Alert] sendRequests : Waiting for CTCL to login"));
				sleep(2);
			}

			// TODO : Add check : if cancel sent already, don't send again
			this->sendCancellation(order);
		}
		else
		{
			if (order.is_new)
				logger->sys_info(fmt::format(
					"CSender : Sleeping for {} secs before sending entry req",
					sleep_timer));
			else
				logger->sys_info(fmt::format(
					"CSender : Sleeping for {} secs before sending modif req",
					sleep_timer));
			sleep(sleep_timer);
			while (!this->getCTCLLoginStatus())
			{
				logger->sys_info(fmt::format(
					"[Teams Alert] sendRequests : Waiting for CTCL to login"));
				sleep(2);
			}
			std::int32_t curr_px;
			if ((curr_ts.tm_hour >= 23) && (curr_ts.tm_min >= (this->exchangeClosingMinute - 10)))
			{
				curr_px = data_rdr->readSymbol(
					order.symbol, (order.direction == 1) ? "ask1" : "bid1");
				logger->sys_info(fmt::format(
					"CSender : Sending Market Order for {} at {}:{}",
					order.symbol,curr_ts.tm_hour,curr_ts.tm_min));
			}
			else
			{
				curr_px = data_rdr->readSymbol(
					order.symbol, (order.direction == 1) ? "bid1" : "ask1");
				if (curr_px == -1)
				{
					logger->ord_error(fmt::format(
						"Current price couldn't be fetched for symbol {}", order.symbol));
				}
				else if (price_tick.contains(order.symbol))
				{
					int additional_price_tick = 1;
					if (order.symbol.find("CRUDEOIL") != std::string::npos) {
						additional_price_tick = 2;
					}
					curr_px += ((order.direction == 1) ? 1 : -1) * additional_price_tick * price_tick[order.symbol];

					// corner case - ask
					if(curr_px <= 0){
						curr_px = price_tick[order.symbol];
					}

					// Round to Tick size 
					curr_px = ((curr_px + price_tick[order.symbol]/2) / price_tick[order.symbol])*price_tick[order.symbol];
				}
			}
			// ! TODO : Handle case where curr_px = -1
			// ! TODO : Handle case where price_tick = 0
			order.ord_px = curr_px;

			if (order.is_new)
			{
				// TODO : Add check : if order sent already, don't send again
				this->sendOrder(order);
			}
			else if (order.is_modif)
			{
				this->sendModification(order);
			}
		}
	}
}

int CSender::updateKiviOrdNum(std::int32_t new_kivi_ord_num)
{
	if (new_kivi_ord_num == 0)
	{
		return 0;
	}
	aurora::updateKiviOrdNumRequest kivi_req;
	kivi_req.set_new_kivi_ord_num(new_kivi_ord_num);
	ClientContext context;
	aurora::Ack ack;
	grpc::Status status = this->stub_->updateKiviOrdNum(&context, kivi_req, &ack);
	if (status.ok())
	{
		logger->sys_info(fmt::format("Set kivi_ord_num to {}", new_kivi_ord_num));
		return 0;
	}
	else
	{
		logger->raise_error(
			fmt::format("GRPC call updateKiviOrdNum failed with error message {}",
						status.error_message()));
		return 1;
	}
}

void CSender::getOrdStatus(std::string exec_ord_num)
{
	aurora::OrderInfo ord_info;
	aurora::OrderPacket ord_packet;
	ord_info.set_exec_ord_num(exec_ord_num);
	ClientContext context;
	aurora::Ack ack;
	grpc::Status status =
		this->stub_->getOrdStatus(&context, ord_info, &ord_packet);
	if (status.ok())
	{
		// ord_packet is returned and needs to be handled.
		this->ord_mngr->processOrdResponses(ord_packet);
	}
	else
	{
		logger->raise_error(fmt::format("GRPC call getOrdStatus failed for "
										"exec_ord_num {0} with error message {1}",
										exec_ord_num, status.error_message()));
	}
	return;
}

bool CSender::getCTCLLoginStatus()
{
	bool login_status = false;
	for (int num = 0; num < NUM_GRPC_RETRIES; num++)
	{
		aurora::Empty emp;
		aurora::LoginStatus loginStatus;
		ClientContext context;
		grpc::Status status =
			this->stub_->getLoginStatus(&context, emp, &loginStatus);
		if (status.ok())
		{
			login_status = loginStatus.login_status();
			break;
		}
		else
		{
			logger->sys_error(
				fmt::format("[Teams Alert] getCTCLLoginStatus : Error getting login "
							"status from CTCL system. Retry {}/{}",
							num + 1, NUM_GRPC_RETRIES));
			sleep(GRPC_RETRY_DELAY);
		}
	}
	return login_status;
}

void CSender::sendLogOffRequest()
{
	aurora::LogoffRequest logoff_req;
	aurora::LogoffResponse logoff_resp;
	ClientContext context;

	grpc::Status status =
		this->stub_->logoffRequest(&context, logoff_req, &logoff_resp);
	if (status.ok())
	{
		// ord_packet is returned and needs to be handled.
		logger->sys_info(fmt::format("Log off request sent to CTCL"));
	}
	else
	{
		logger->raise_error(
			fmt::format("GRPC call logoff req failed with error message {0}",
						status.error_message()));
	}
	return;
}
