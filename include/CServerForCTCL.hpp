/**
 * @file CReceiver.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2021-08-03
 * 
 * @copyright Copyright (c) 2021
 * 
 */
#ifndef CSERVERFORCTCL_HPP
#define CSERVERFORCTCL_HPP

#include "sky.grpc.pb.h"
#include <grpcpp/ext/proto_server_reflection_plugin.h>
#include <grpcpp/grpcpp.h>
#include <grpcpp/health_check_service_interface.h>
#include "structures.h"
#include "CUtility.hpp"
#include "COrderManager.hpp"

using grpc::Server;
using grpc::ServerBuilder;
using grpc::ServerContext;

using aurora::CTCLToExecHandler;

class COrderManager;

class CServerForCTCL final : public CTCLToExecHandler::Service
{
    std::unique_ptr<Server> server;
    COrderManager *ord_mngr; // = nullptr;

public:
    CServerForCTCL();
    virtual ~CServerForCTCL() = default;
    void serve(void *v);
	void stopServer();
    void setOrdManager(COrderManager *_ord_mngr);
    grpc::Status sendOrdInfo(ServerContext *context, const aurora::OrderPacket *req, aurora::Ack *ack);
    grpc::Status sendTrdInfo(ServerContext *context, const aurora::TradePacket *req, aurora::Ack *ack);
};

#endif