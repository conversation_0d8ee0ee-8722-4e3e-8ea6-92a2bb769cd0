from logging import raiseExceptions
import grpc
import sys

sys.path.append("/usr/src/mcx-execution-system/protos/")
import receiver_pb2
import receiver_pb2_grpc
import sky_pb2
import sky_pb2_grpc
import time


def send_order():
    with grpc.insecure_channel(grpc_server) as channel:
        stub = receiver_pb2_grpc.RecvSignalHandlerStub(channel)
        req = receiver_pb2.OrderEntryRequest()

        req.symbol = "NATURALGAS24-Nov-2021"
        req.ord_type = "NORMAL"
        req.ord_px = 0
        req.direction = 1
        req.ord_qty = 3
        req.exec_trade_id = "10011101653"
        req.strat_name = "cluster_mcx_old"
        response = stub.sendOrder(req)
    print(response)


def update_ids():
    with grpc.insecure_channel(grpc_server) as channel:
        stub = receiver_pb2_grpc.RecvSignalHandlerStub(channel)
        req = receiver_pb2.IdUpdateRequest()

        req.new_kivi_ord_num = 100
        req.new_exec_ord_num = ""
        response = stub.updateIds(req)
    print(response)

def clearCurrExecSymbol():
    with grpc.insecure_channel(grpc_server) as channel:
        stub = receiver_pb2_grpc.RecvSignalHandlerStub(channel)
        req = receiver_pb2.CurrExecSymbol()
        req.symbol = "NATURALGAS24-Nov-2021"
        response = stub.clearCurrExecSymbol(req)
    print(response)

def send_trades():
    with grpc.insecure_channel(exec_server) as channel:
        stub = sky_pb2_grpc.CTCLToExecHandlerStub(channel)
        req = sky_pb2.TradePacket()

        req.exec_ord_num = "100003"
        req.kivi_ord_num = 5
        req.exch_ord_num = 332132100015424
        req.trd_num = 510002070
        req.symbol = "NATURALGAS24-Nov-2021"
        req.trade_price = 38250
        req.direction = 2
        req.quantity = 4
        req.trade_ts = 0
        response = stub.sendTrdInfo(req)


grpc_server = "0.0.0.0:7779"
exec_server = "0.0.0.0:8778"
if __name__ == "__main__":
    # send_order()
    send_trades()
    # clearCurrExecSymbol()
    # update_ids()
