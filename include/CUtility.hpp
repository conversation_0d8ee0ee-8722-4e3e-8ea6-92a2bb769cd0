/**
 * @file CUtility.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2021-08-03
 * 
 * @copyright Copyright (c) 2021
 * 
 */
#ifndef CUTILITY_HPP
#define CUTILITY_HPP
#include "CGlobals.hpp"
#include "SimpleIni/SimpleIni.h"
#include <fstream>
#include <random>
#include <set>
#include <chrono>
#include <ctime>
#include <sstream>
#include <time.h>
#include "TradingLog.pb.h"
#include "google/protobuf/util/time_util.h"
#include <google/protobuf/timestamp.pb.h>
#define CONFIG_FILE_PATH "../Config.ini"

namespace CUtility
{
	time_t getEpochTime();
	struct tm getCurrTimeStamp();
	std::string getCurrTimeStampStr(std::string s_format = "%Y-%m-%d %H:%M:%S");
	std::string getCurrDay(std::string s_format);
	std::string convertEpochToTS(uint32_t epoch, std::string s_format = "%Y-%m-%d %H:%M:%S");
	std::uint32_t convertTSToEpoch(std::string ts, std::string s_format);
	std::string getConfig(std::string section, std::string var_name, std::string default_val = "");
	void setConfig(std::string section, std::string var_name, std::string value);
	std::string rstrip(std::string word);
	void split(std::string const &line, const char delim, std::vector<std::string> &words);
	int updateExchangeClosing();
	void populateValueCoeff();
	int getRandomDelay();
	Contract splitContractName(std::string contract_name);
	ExecutionLogs::BaseLog getBaseLogObject();
	google::protobuf::Timestamp TimeStringToTimestamp(const std::string& time_str);
} // namespace CUtility
#endif
