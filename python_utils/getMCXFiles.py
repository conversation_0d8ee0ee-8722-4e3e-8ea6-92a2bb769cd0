import io
import os
from minio import Minio

url = "*************:11009"
access_key = "super"
secret_key = "doopersecret"
secure = False

mcx_support_files_path = "/data/mcx_support_files/"

minioClient = Minio(
    url,
    access_key=access_key,
    secret_key=secret_key,
    secure=secure,
)
if not os.path.exists(mcx_support_files_path):
    os.makedirs(mcx_support_files_path)
minioClient.fget_object(
    "commondata",
    "mcx_daily_downloads/MCXScrips.csv",
    os.path.join(mcx_support_files_path, "MCXScrips.csv"),
)
minioClient.fget_object(
    "commondata",
    "balte_uploads/mcx_daylight_savings.csv",
    "/data/mcx_support_files/mcx_daylight_savings.csv",
)
