/**
 * @file CReader.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2021-08-20
 * 
 * @copyright Copyright (c) 2021
 * 
 */
#include "CUtility.hpp"
#include <sw/redis++/redis++.h>

using namespace sw::redis;

/**
 * @brief Class to read records from Redis.
 *
 */
class CReader
{
	Redis *redis;
	std::string r_symbols_list;

public:
	/**
	 * @brief Construct a new CReader object.
	 *
	 */
	CReader()
	{
		std::string redis_ip, redis_port;
		redis_ip = CUtility::getConfig("Redis", "redis_ip", "127.0.0.1");
		redis_port = CUtility::getConfig("Redis", "redis_port", "6379");
		redis = new Redis("tcp://" + redis_ip + ":" + redis_port);
		logger->sys_info("Connection to Redis established");
		this->r_symbols_list =
			CUtility::getConfig("Redis", "symbols_list", "modified_symbols");
	}

	/**
	 * @brief Return the value of given key
	 *
	 * @param key
	 * @return std::string
	 */
	std::string readKey(std::string key)
	{
		auto val = redis->get(key);
		if (val)
		{
			return *val;
		}
		return "";
	}

	std::int32_t readSymbol(std::string symbol, std::string column="bid1")
	{
		std::string ord_snap = this->readKey(symbol);
		if (ord_snap == "")
			return -1;
		std::vector<std::string> values;
		CUtility::split(ord_snap, '|', values);
		if(column == "bid1"){
			float hundred = 100;
			std::int32_t px = (std::stof(values[7]))*hundred;
			return px;
		}
		else if(column == "ask1"){
			float hundred = 100;
			std::int32_t px = (std::stof(values[22]))*hundred;
			return px;
		}
		else if (column == "ltp")
			return std::stoi(values[3]);
		else if (column == "bid1_vol")
			return std::stoi(values[6]);
		else if (column == "ask1_vol")
			return std::stoi(values[21]);
		return -1;
	}
};